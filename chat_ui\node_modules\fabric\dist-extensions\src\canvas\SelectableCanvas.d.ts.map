{"version": 3, "file": "SelectableCanvas.d.ts", "sourceRoot": "", "sources": ["../../../src/canvas/SelectableCanvas.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAC7D,OAAO,KAAK,EACV,YAAY,EACZ,WAAW,EACX,oBAAoB,EACpB,aAAa,EACb,SAAS,EACV,MAAM,kBAAkB,CAAC;AAK1B,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAG9C,OAAO,KAAK,EACV,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,WAAW,EACZ,MAAM,aAAa,CAAC;AAIrB,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAItD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAgBlE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAKrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6FG;AACH,qBAAa,gBAAgB,CAAC,SAAS,SAAS,YAAY,GAAG,YAAY,CACzE,SAAQ,YAAY,CAAC,SAAS,CAC9B,YAAW,IAAI,CAAC,aAAa,EAAE,qBAAqB,CAAC;IAE7C,QAAQ,EAAE,YAAY,EAAE,CAAC;IAGzB,cAAc,EAAE,OAAO,CAAC;IACxB,WAAW,EAAE,oBAAoB,CAAC;IAClC,eAAe,EAAE,OAAO,CAAC;IACzB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,WAAW,EAAE,oBAAoB,CAAC;IAClC,YAAY,EAAE,oBAAoB,CAAC;IAGnC,SAAS,EAAE,OAAO,CAAC;IACnB,YAAY,EAAE,oBAAoB,GAAG,WAAW,EAAE,CAAC;IACnD,eAAe,EAAE,oBAAoB,CAAC;IACtC,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,MAAM,EAAE,CAAC;IAC7B,oBAAoB,EAAE,MAAM,CAAC;IAC7B,kBAAkB,EAAE,MAAM,CAAC;IAC3B,uBAAuB,EAAE,OAAO,CAAC;IAGjC,WAAW,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC3C,UAAU,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC1C,aAAa,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC7C,iBAAiB,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACjD,gBAAgB,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAEhD,cAAc,EAAE,MAAM,CAAC;IAGvB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,cAAc,EAAE,OAAO,CAAC;IAEhC;;;;;;;OAOG;IACK,aAAa,EAAE,OAAO,CAAC;IAEvB,sBAAsB,EAAE,OAAO,CAAC;IAGhC,eAAe,EAAE,OAAO,CAAC;IACzB,cAAc,EAAE,OAAO,CAAC;IACxB,eAAe,EAAE,OAAO,CAAC;IAEjC;;;OAGG;IACH,OAAO,EAAE,YAAY,EAAE,CAAM;IAE7B;;;;OAIG;IACK,cAAc,CAAC,EAAE,YAAY,CAAC;IAEtC;;;;OAIG;IACH,eAAe,EAAE,YAAY,EAAE,CAAM;IAErC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,YAAY,EAAE,CAAC;IAElC;;;;;OAKG;IACH,iBAAiB,EAAE,SAAS,GAAG,IAAI,CAAQ;IAE3C;;;;;;;OAOG;IACH,SAAS,CAAC,cAAc,EAAE;QACxB,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;KAChB,GAAG,IAAI,CAAQ;IAEhB;;;;;OAKG;IACH,eAAe,UAAS;IAExB;;;;;;OAMG;IACH,UAAkB,gBAAgB,CAAC,EAAE,KAAK,CAAC;IAE3C;;;;;;OAMG;IACH,UAAkB,QAAQ,CAAC,EAAE,KAAK,CAAC;IAEnC;;;;;OAKG;IACH,UAAkB,OAAO,CAAC,EAAE,YAAY,CAAC;IAEzC,MAAM,CAAC,WAAW,gDAAkB;IAEpC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAIjC,QAAQ,EAAE,gBAAgB,CAAC;IACnC,IAAI,aAAa,sBAEhB;IACD,IAAI,UAAU,6BAEb;IACD,IAAI,SAAS,mBAEZ;IACD,QAAgB,iBAAiB,CAAoB;IACrD,QAAgB,gBAAgB,CAA2B;IAE3D,UAAkB,mBAAmB,EAAE,OAAO,CAAC;IACvC,gBAAgB,CAAC,EAAE,SAAS,CAAC;IAC7B,aAAa,CAAC,EAAE,YAAY,CAAC;IAErC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,iBAAiB;IAQtD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,YAAY;IAKhC;;;OAGG;IACH,gBAAgB,CAAC,GAAG,EAAE,YAAY;IAkBlC,oBAAoB;IAKpB;;;;OAIG;IACH,sBAAsB,IAAI,YAAY,EAAE;IASxC;;OAEG;IACH,SAAS;IAkBT;;OAEG;IACH,cAAc,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI;IAcnD;;;;OAIG;IACH,SAAS;IAQT;;;;OAIG;IACH,sBAAsB,CAAC,KAAK,EAAE,MAAM;IASpC;;;;;;;;;OASG;IACH,mBAAmB,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO;IAuBxE;;;;OAIG;IACH,sBAAsB,CAAC,CAAC,EAAE,aAAa,GAAG,OAAO;IAYjD;;;;OAIG;IACH,qBAAqB,CACnB,CAAC,EAAE,aAAa,EAChB,MAAM,CAAC,EAAE,YAAY,GACpB,MAAM,IAAI,SAAS;IAiBtB;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,sBAAsB;IAyB9B;;;;;;OAMG;IACH,oBAAoB,CAClB,MAAM,EAAE,YAAY,EACpB,WAAW,EAAE,MAAM,GAClB;QAAE,CAAC,EAAE,QAAQ,CAAC;QAAC,CAAC,EAAE,QAAQ,CAAA;KAAE;IA2B/B;;;;;OAKG;IACH,sBAAsB,CACpB,CAAC,EAAE,aAAa,EAChB,MAAM,EAAE,YAAY,EACpB,eAAe,EAAE,OAAO,GACvB,IAAI;IA6DP;;;;OAIG;IACH,SAAS,CAAC,KAAK,EAAE,mBAAmB,CAAC,QAAQ,CAAC,GAAG,IAAI;IAIrD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI;IAqCnD;;;;;;OAMG;IACH,UAAU,CAAC,CAAC,EAAE,aAAa,GAAG,YAAY,GAAG,SAAS;IAkDtD;;;;;;OAMG;IACH,OAAO,CAAC,6BAA6B;IAkCrC;;;;;;;OAOG;IACH,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,GAAG,OAAO;IAwBxD;;;;;;OAMG;IACH,sBAAsB,CACpB,OAAO,EAAE,YAAY,EAAE,EACvB,OAAO,EAAE,KAAK,GACb,YAAY,GAAG,SAAS;IAoB3B;;;;;;OAMG;IACH,qBAAqB,CACnB,OAAO,EAAE,YAAY,EAAE,EACvB,OAAO,EAAE,KAAK,GACb,YAAY,GAAG,SAAS;IA6B3B;;;;;;;;;;;;;OAaG;IACH,gBAAgB,CAAC,CAAC,EAAE,aAAa;IAOjC;;;;;;;;;;;OAWG;IACH,aAAa,CAAC,CAAC,EAAE,aAAa;IAO9B;;;;;;;;;OASG;IACH,UAAU,CAAC,CAAC,EAAE,aAAa,EAAE,YAAY,UAAQ,GAAG,KAAK;IAyCzD;;;OAGG;IACH,SAAS,CAAC,kBAAkB,CAC1B,UAAU,EAAE,KAAK,EACjB,OAAO,CAAC,EAAE,kBAAkB;IAW9B,SAAS,CAAC,kBAAkB;IAQ5B;;;OAGG;IACH,aAAa,IAAI,wBAAwB;IAIzC;;;;OAIG;IACH,mBAAmB,IAAI,wBAAwB;IAI/C;;;OAGG;IACH,mBAAmB,IAAI,iBAAiB;IAIxC;;;OAGG;IACH,eAAe,IAAI,YAAY,GAAG,SAAS;IAI3C;;;OAGG;IACH,gBAAgB,IAAI,YAAY,EAAE;IASlC;;;;;OAKG;IACH,oBAAoB,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE,aAAa;IAqDlE;;;;;OAKG;IACH,eAAe,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,aAAa;IAQvD;;;;;;;OAOG;IACH,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,EAAE,aAAa;IAwBxD;;;;;;;OAOG;IACH,oBAAoB,CAClB,CAAC,CAAC,EAAE,aAAa,EACjB,MAAM,CAAC,EAAE,YAAY,GACpB,IAAI,IAAI;QAAE,aAAa,EAAE,SAAS,CAAA;KAAE;IAmBvC;;;;;;;OAOG;IACH,mBAAmB,CAAC,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI,IAAI;QAAE,aAAa,EAAE,SAAS,CAAA;KAAE;IAc5E;;;;;OAKG;IACH,mBAAmB,CAAC,CAAC,CAAC,EAAE,aAAa;IAUrC;;;OAGG;IACH,yBAAyB,CAAC,CAAC,CAAC,EAAE,aAAa;IAsB3C;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM;IAQhC;;OAEG;IACH,OAAO;IAqBP;;OAEG;IACH,KAAK;IASL;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,wBAAwB;IAQ1C;;OAEG;IACH,SAAS,CAAC,SAAS,CACjB,QAAQ,EAAE,YAAY,EACtB,UAAU,EAAE,UAAU,GAAG,kBAAkB,EAC3C,mBAAmB,EAAE,MAAM,EAAE,GAC5B,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAYtB;;;;;OAKG;IACH,OAAO,CAAC,8BAA8B;IAwBtC;;OAEG;IACH,aAAa,CACX,MAAM,EAAE,MAAM,EAAE,EAChB,QAAQ,EAAE,YAAY,EACtB,OAAO,CAAC,EAAE,WAAW;CAQxB"}