'use client';

import { useImageEditor } from './context';
import * as fabric from 'fabric';

const Toolbar = () => {
  const { canvas } = useImageEditor();

  const addRectangle = () => {
    if (canvas) {
      const rect = new fabric.Rect({
        left: 100,
        top: 100,
        fill: 'red',
        width: 200,
        height: 100,
      });
      canvas.add(rect);
      canvas.renderAll();
    }
  };

  return (
    <div style={{ marginBottom: '10px' }}>
      <button onClick={addRectangle}>Add Rectangle</button>
    </div>
  );
};

export default Toolbar;
