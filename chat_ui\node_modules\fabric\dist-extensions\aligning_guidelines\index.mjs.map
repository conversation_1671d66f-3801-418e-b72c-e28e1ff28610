{"version": 3, "file": "index.mjs", "sources": ["../../extensions/aligning_guidelines/index.ts"], "sourcesContent": ["import type {\n  BasicTransformEvent,\n  Canvas,\n  FabricObject,\n  TBBox,\n  TPointerEvent,\n} from 'fabric';\nimport { Point, util } from 'fabric';\nimport {\n  collectHorizontalPoint,\n  collectVerticalPoint,\n} from './util/collect-point';\nimport {\n  drawHorizontalLine,\n  drawPointList,\n  drawVerticalLine,\n} from './util/draw';\nimport { getObjectsByTarget } from './util/get-objects-by-target';\nimport { collectLine } from './util/collect-line';\nimport type {\n  AligningLineConfig,\n  HorizontalLine,\n  VerticalLine,\n} from './typedefs';\nimport { aligningLineConfig } from './constant';\n\ntype TransformEvent = BasicTransformEvent<TPointerEvent> & {\n  target: FabricObject;\n};\n\nexport type { AligningLineConfig } from './typedefs';\n\nexport function initAligningGuidelines(\n  canvas: Canvas,\n  options: Partial<AligningLineConfig> = {},\n) {\n  Object.assign(aligningLineConfig, options);\n\n  const horizontalLines = new Set<string>();\n  const verticalLines = new Set<string>();\n  let onlyDrawPoint = false;\n  const cacheMap = new Map<string, [TBBox, Point[]]>();\n\n  const getCaCheMapValue = (object: FabricObject) => {\n    const cacheKey = [\n      object.calcTransformMatrix().toString(),\n      object.width,\n      object.height,\n    ].join();\n    const cacheValue = cacheMap.get(cacheKey);\n    if (cacheValue) return cacheValue;\n    const coords = object.getCoords();\n    const rect = util.makeBoundingBoxFromPoints(coords);\n    const value: [TBBox, Point[]] = [rect, coords];\n    cacheMap.set(cacheKey, value);\n    return value;\n  };\n\n  function moving(e: TransformEvent) {\n    const activeObject = e.target;\n    activeObject.setCoords();\n    onlyDrawPoint = false;\n    verticalLines.clear();\n    horizontalLines.clear();\n\n    const objects = getObjectsByTarget(activeObject);\n    const activeObjectRect = activeObject.getBoundingRect();\n\n    for (const object of objects) {\n      const objectRect = getCaCheMapValue(object)[0];\n      const { vLines, hLines } = collectLine({\n        activeObject,\n        activeObjectRect,\n        objectRect,\n      });\n      vLines.forEach((o) => {\n        verticalLines.add(JSON.stringify(o));\n      });\n      hLines.forEach((o) => {\n        horizontalLines.add(JSON.stringify(o));\n      });\n    }\n  }\n\n  function scalingOrResizing(e: TransformEvent) {\n    // br bl tr tl mb ml mt mr\n    const activeObject = e.target;\n    activeObject.setCoords();\n    const isScale = String(e.transform.action).startsWith('scale');\n    verticalLines.clear();\n    horizontalLines.clear();\n\n    const objects = getObjectsByTarget(activeObject);\n    let corner = e.transform.corner;\n    if (activeObject.flipX) corner = corner.replace('l', 'r').replace('r', 'l');\n    if (activeObject.flipY) corner = corner.replace('t', 'b').replace('b', 't');\n    let index = ['tl', 'tr', 'br', 'bl', 'mt', 'mr', 'mb', 'ml'].indexOf(\n      corner,\n    );\n    if (index == -1) return;\n    onlyDrawPoint = index > 3;\n    if (onlyDrawPoint) {\n      const angle = activeObject.getTotalAngle();\n      if (angle % 90 != 0) return;\n      index -= 4;\n    }\n    let point = activeObject.getCoords()[index];\n    for (const object of objects) {\n      const [rect, coords] = getCaCheMapValue(object);\n      const center = new Point(\n        rect.left + rect.width / 2,\n        rect.top + rect.height / 2,\n      );\n      const list = [...coords, center];\n      const props = { activeObject, point, list, isScale, index };\n      const vLines = collectVerticalPoint(props);\n      const hLines = collectHorizontalPoint(props);\n      vLines.forEach((o) => {\n        verticalLines.add(JSON.stringify(o));\n      });\n      hLines.forEach((o) => {\n        horizontalLines.add(JSON.stringify(o));\n      });\n      if (vLines.length || hLines.length)\n        point = activeObject.getCoords()[index];\n    }\n  }\n\n  function beforeRender() {\n    canvas.clearContext(canvas.contextTop);\n  }\n  function afterRender() {\n    if (onlyDrawPoint) {\n      const list: Array<VerticalLine | HorizontalLine> = [];\n      for (const v of verticalLines) list.push(JSON.parse(v));\n      for (const h of horizontalLines) list.push(JSON.parse(h));\n      drawPointList(canvas, list);\n    } else {\n      for (const v of verticalLines) drawVerticalLine(canvas, JSON.parse(v));\n      for (const h of horizontalLines)\n        drawHorizontalLine(canvas, JSON.parse(h));\n    }\n  }\n  function mouseUp() {\n    verticalLines.clear();\n    horizontalLines.clear();\n    cacheMap.clear();\n    canvas.requestRenderAll();\n  }\n\n  canvas.on('object:resizing', scalingOrResizing);\n  canvas.on('object:scaling', scalingOrResizing);\n  canvas.on('object:moving', moving);\n  canvas.on('before:render', beforeRender);\n  canvas.on('after:render', afterRender);\n  canvas.on('mouse:up', mouseUp);\n\n  return () => {\n    canvas.off('object:resizing', scalingOrResizing);\n    canvas.off('object:scaling', scalingOrResizing);\n    canvas.off('object:moving', moving);\n    canvas.off('before:render', beforeRender);\n    canvas.off('after:render', afterRender);\n    canvas.off('mouse:up', mouseUp);\n  };\n}\n"], "names": ["initAligningGuidelines", "canvas", "options", "arguments", "length", "undefined", "Object", "assign", "aligningLineConfig", "horizontalLines", "Set", "verticalLines", "onlyDrawPoint", "cacheMap", "Map", "getCaCheMapValue", "object", "cache<PERSON>ey", "calcTransformMatrix", "toString", "width", "height", "join", "cacheValue", "get", "coords", "getCoords", "rect", "util", "makeBoundingBoxFromPoints", "value", "set", "moving", "e", "activeObject", "target", "setCoords", "clear", "objects", "getObjectsByTarget", "activeObjectRect", "getBoundingRect", "objectRect", "vLines", "hLines", "collectLine", "for<PERSON>ach", "o", "add", "JSON", "stringify", "scalingOrResizing", "isScale", "String", "transform", "action", "startsWith", "corner", "flipX", "replace", "flipY", "index", "indexOf", "angle", "getTotalAngle", "point", "center", "Point", "left", "top", "list", "props", "collectVerticalPoint", "collectHorizontalPoint", "beforeRender", "clearContext", "contextTop", "afterRender", "v", "push", "parse", "h", "drawPointList", "drawVerticalLine", "drawHorizontalLine", "mouseUp", "requestRenderAll", "on", "off"], "mappings": ";;;;;;;AAgCO,SAASA,sBAAsBA,CACpCC,MAAc,EAEd;AAAA,EAAA,IADAC,OAAoC,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;AAEzCG,EAAAA,MAAM,CAACC,MAAM,CAACC,kBAAkB,EAAEN,OAAO,CAAC,CAAA;AAE1C,EAAA,MAAMO,eAAe,GAAG,IAAIC,GAAG,EAAU,CAAA;AACzC,EAAA,MAAMC,aAAa,GAAG,IAAID,GAAG,EAAU,CAAA;EACvC,IAAIE,aAAa,GAAG,KAAK,CAAA;AACzB,EAAA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAA4B,CAAA;EAEpD,MAAMC,gBAAgB,GAAIC,MAAoB,IAAK;IACjD,MAAMC,QAAQ,GAAG,CACfD,MAAM,CAACE,mBAAmB,EAAE,CAACC,QAAQ,EAAE,EACvCH,MAAM,CAACI,KAAK,EACZJ,MAAM,CAACK,MAAM,CACd,CAACC,IAAI,EAAE,CAAA;AACR,IAAA,MAAMC,UAAU,GAAGV,QAAQ,CAACW,GAAG,CAACP,QAAQ,CAAC,CAAA;IACzC,IAAIM,UAAU,EAAE,OAAOA,UAAU,CAAA;AACjC,IAAA,MAAME,MAAM,GAAGT,MAAM,CAACU,SAAS,EAAE,CAAA;AACjC,IAAA,MAAMC,IAAI,GAAGC,IAAI,CAACC,yBAAyB,CAACJ,MAAM,CAAC,CAAA;AACnD,IAAA,MAAMK,KAAuB,GAAG,CAACH,IAAI,EAAEF,MAAM,CAAC,CAAA;AAC9CZ,IAAAA,QAAQ,CAACkB,GAAG,CAACd,QAAQ,EAAEa,KAAK,CAAC,CAAA;AAC7B,IAAA,OAAOA,KAAK,CAAA;GACb,CAAA;EAED,SAASE,MAAMA,CAACC,CAAiB,EAAE;AACjC,IAAA,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAAA;IAC7BD,YAAY,CAACE,SAAS,EAAE,CAAA;AACxBxB,IAAAA,aAAa,GAAG,KAAK,CAAA;IACrBD,aAAa,CAAC0B,KAAK,EAAE,CAAA;IACrB5B,eAAe,CAAC4B,KAAK,EAAE,CAAA;AAEvB,IAAA,MAAMC,OAAO,GAAGC,kBAAkB,CAACL,YAAY,CAAC,CAAA;AAChD,IAAA,MAAMM,gBAAgB,GAAGN,YAAY,CAACO,eAAe,EAAE,CAAA;AAEvD,IAAA,KAAK,MAAMzB,MAAM,IAAIsB,OAAO,EAAE;MAC5B,MAAMI,UAAU,GAAG3B,gBAAgB,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;MAC9C,MAAM;QAAE2B,MAAM;AAAEC,QAAAA,MAAAA;OAAQ,GAAGC,WAAW,CAAC;QACrCX,YAAY;QACZM,gBAAgB;AAChBE,QAAAA,UAAAA;AACF,OAAC,CAAC,CAAA;AACFC,MAAAA,MAAM,CAACG,OAAO,CAAEC,CAAC,IAAK;QACpBpC,aAAa,CAACqC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACH,CAAC,CAAC,CAAC,CAAA;AACtC,OAAC,CAAC,CAAA;AACFH,MAAAA,MAAM,CAACE,OAAO,CAAEC,CAAC,IAAK;QACpBtC,eAAe,CAACuC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACH,CAAC,CAAC,CAAC,CAAA;AACxC,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;EAEA,SAASI,iBAAiBA,CAAClB,CAAiB,EAAE;AAC5C;AACA,IAAA,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAAA;IAC7BD,YAAY,CAACE,SAAS,EAAE,CAAA;AACxB,IAAA,MAAMgB,OAAO,GAAGC,MAAM,CAACpB,CAAC,CAACqB,SAAS,CAACC,MAAM,CAAC,CAACC,UAAU,CAAC,OAAO,CAAC,CAAA;IAC9D7C,aAAa,CAAC0B,KAAK,EAAE,CAAA;IACrB5B,eAAe,CAAC4B,KAAK,EAAE,CAAA;AAEvB,IAAA,MAAMC,OAAO,GAAGC,kBAAkB,CAACL,YAAY,CAAC,CAAA;AAChD,IAAA,IAAIuB,MAAM,GAAGxB,CAAC,CAACqB,SAAS,CAACG,MAAM,CAAA;IAC/B,IAAIvB,YAAY,CAACwB,KAAK,EAAED,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC3E,IAAIzB,YAAY,CAAC0B,KAAK,EAAEH,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC3E,IAAIE,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACC,OAAO,CAClEL,MACF,CAAC,CAAA;AACD,IAAA,IAAII,KAAK,IAAI,CAAC,CAAC,EAAE,OAAA;IACjBjD,aAAa,GAAGiD,KAAK,GAAG,CAAC,CAAA;AACzB,IAAA,IAAIjD,aAAa,EAAE;AACjB,MAAA,MAAMmD,KAAK,GAAG7B,YAAY,CAAC8B,aAAa,EAAE,CAAA;AAC1C,MAAA,IAAID,KAAK,GAAG,EAAE,IAAI,CAAC,EAAE,OAAA;AACrBF,MAAAA,KAAK,IAAI,CAAC,CAAA;AACZ,KAAA;IACA,IAAII,KAAK,GAAG/B,YAAY,CAACR,SAAS,EAAE,CAACmC,KAAK,CAAC,CAAA;AAC3C,IAAA,KAAK,MAAM7C,MAAM,IAAIsB,OAAO,EAAE;MAC5B,MAAM,CAACX,IAAI,EAAEF,MAAM,CAAC,GAAGV,gBAAgB,CAACC,MAAM,CAAC,CAAA;MAC/C,MAAMkD,MAAM,GAAG,IAAIC,KAAK,CACtBxC,IAAI,CAACyC,IAAI,GAAGzC,IAAI,CAACP,KAAK,GAAG,CAAC,EAC1BO,IAAI,CAAC0C,GAAG,GAAG1C,IAAI,CAACN,MAAM,GAAG,CAC3B,CAAC,CAAA;AACD,MAAA,MAAMiD,IAAI,GAAG,CAAC,GAAG7C,MAAM,EAAEyC,MAAM,CAAC,CAAA;AAChC,MAAA,MAAMK,KAAK,GAAG;QAAErC,YAAY;QAAE+B,KAAK;QAAEK,IAAI;QAAElB,OAAO;AAAES,QAAAA,KAAAA;OAAO,CAAA;AAC3D,MAAA,MAAMlB,MAAM,GAAG6B,oBAAoB,CAACD,KAAK,CAAC,CAAA;AAC1C,MAAA,MAAM3B,MAAM,GAAG6B,sBAAsB,CAACF,KAAK,CAAC,CAAA;AAC5C5B,MAAAA,MAAM,CAACG,OAAO,CAAEC,CAAC,IAAK;QACpBpC,aAAa,CAACqC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACH,CAAC,CAAC,CAAC,CAAA;AACtC,OAAC,CAAC,CAAA;AACFH,MAAAA,MAAM,CAACE,OAAO,CAAEC,CAAC,IAAK;QACpBtC,eAAe,CAACuC,GAAG,CAACC,IAAI,CAACC,SAAS,CAACH,CAAC,CAAC,CAAC,CAAA;AACxC,OAAC,CAAC,CAAA;AACF,MAAA,IAAIJ,MAAM,CAACvC,MAAM,IAAIwC,MAAM,CAACxC,MAAM,EAChC6D,KAAK,GAAG/B,YAAY,CAACR,SAAS,EAAE,CAACmC,KAAK,CAAC,CAAA;AAC3C,KAAA;AACF,GAAA;EAEA,SAASa,YAAYA,GAAG;AACtBzE,IAAAA,MAAM,CAAC0E,YAAY,CAAC1E,MAAM,CAAC2E,UAAU,CAAC,CAAA;AACxC,GAAA;EACA,SAASC,WAAWA,GAAG;AACrB,IAAA,IAAIjE,aAAa,EAAE;MACjB,MAAM0D,IAA0C,GAAG,EAAE,CAAA;AACrD,MAAA,KAAK,MAAMQ,CAAC,IAAInE,aAAa,EAAE2D,IAAI,CAACS,IAAI,CAAC9B,IAAI,CAAC+B,KAAK,CAACF,CAAC,CAAC,CAAC,CAAA;AACvD,MAAA,KAAK,MAAMG,CAAC,IAAIxE,eAAe,EAAE6D,IAAI,CAACS,IAAI,CAAC9B,IAAI,CAAC+B,KAAK,CAACC,CAAC,CAAC,CAAC,CAAA;AACzDC,MAAAA,aAAa,CAACjF,MAAM,EAAEqE,IAAI,CAAC,CAAA;AAC7B,KAAC,MAAM;AACL,MAAA,KAAK,MAAMQ,CAAC,IAAInE,aAAa,EAAEwE,gBAAgB,CAAClF,MAAM,EAAEgD,IAAI,CAAC+B,KAAK,CAACF,CAAC,CAAC,CAAC,CAAA;AACtE,MAAA,KAAK,MAAMG,CAAC,IAAIxE,eAAe,EAC7B2E,kBAAkB,CAACnF,MAAM,EAAEgD,IAAI,CAAC+B,KAAK,CAACC,CAAC,CAAC,CAAC,CAAA;AAC7C,KAAA;AACF,GAAA;EACA,SAASI,OAAOA,GAAG;IACjB1E,aAAa,CAAC0B,KAAK,EAAE,CAAA;IACrB5B,eAAe,CAAC4B,KAAK,EAAE,CAAA;IACvBxB,QAAQ,CAACwB,KAAK,EAAE,CAAA;IAChBpC,MAAM,CAACqF,gBAAgB,EAAE,CAAA;AAC3B,GAAA;AAEArF,EAAAA,MAAM,CAACsF,EAAE,CAAC,iBAAiB,EAAEpC,iBAAiB,CAAC,CAAA;AAC/ClD,EAAAA,MAAM,CAACsF,EAAE,CAAC,gBAAgB,EAAEpC,iBAAiB,CAAC,CAAA;AAC9ClD,EAAAA,MAAM,CAACsF,EAAE,CAAC,eAAe,EAAEvD,MAAM,CAAC,CAAA;AAClC/B,EAAAA,MAAM,CAACsF,EAAE,CAAC,eAAe,EAAEb,YAAY,CAAC,CAAA;AACxCzE,EAAAA,MAAM,CAACsF,EAAE,CAAC,cAAc,EAAEV,WAAW,CAAC,CAAA;AACtC5E,EAAAA,MAAM,CAACsF,EAAE,CAAC,UAAU,EAAEF,OAAO,CAAC,CAAA;AAE9B,EAAA,OAAO,MAAM;AACXpF,IAAAA,MAAM,CAACuF,GAAG,CAAC,iBAAiB,EAAErC,iBAAiB,CAAC,CAAA;AAChDlD,IAAAA,MAAM,CAACuF,GAAG,CAAC,gBAAgB,EAAErC,iBAAiB,CAAC,CAAA;AAC/ClD,IAAAA,MAAM,CAACuF,GAAG,CAAC,eAAe,EAAExD,MAAM,CAAC,CAAA;AACnC/B,IAAAA,MAAM,CAACuF,GAAG,CAAC,eAAe,EAAEd,YAAY,CAAC,CAAA;AACzCzE,IAAAA,MAAM,CAACuF,GAAG,CAAC,cAAc,EAAEX,WAAW,CAAC,CAAA;AACvC5E,IAAAA,MAAM,CAACuF,GAAG,CAAC,UAAU,EAAEH,OAAO,CAAC,CAAA;GAChC,CAAA;AACH;;;;"}