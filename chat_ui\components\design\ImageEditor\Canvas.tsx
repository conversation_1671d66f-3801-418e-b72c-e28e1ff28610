'use client';

import { useEffect, useRef } from 'react';
import * as fabric from 'fabric';
import { useImageEditor } from './context';

interface CanvasProps {
  width: number;
  height: number;
}

const Canvas = ({ width, height }: CanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { setCanvas } = useImageEditor();

  useEffect(() => {
    if (canvasRef.current) {
      const canvas = new fabric.Canvas(canvasRef.current, {
        width,
        height,
        backgroundColor: '#f0f0f0',
      });
      setCanvas(canvas);

      // Clean up on unmount
      return () => {
        canvas.dispose();
      };
    }
  }, [width, height, setCanvas]);

  return <canvas ref={canvasRef} />;
};

export default Canvas;
