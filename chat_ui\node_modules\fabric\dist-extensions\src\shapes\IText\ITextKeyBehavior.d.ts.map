{"version": 3, "file": "ITextKeyBehavior.d.ts", "sourceRoot": "", "sources": ["../../../../src/shapes/IText/ITextKeyBehavior.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,KAAK,EAAE,SAAS,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AAMnE,8BAAsB,gBAAgB,CACpC,KAAK,SAAS,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,EACtD,MAAM,SAAS,mBAAmB,GAAG,mBAAmB,EACxD,SAAS,SAAS,WAAW,GAAG,WAAW,CAC3C,SAAQ,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;IAC/C;;;;;;;;;;OAUG;IACK,OAAO,EAAE,YAAY,CAAC;IAEtB,UAAU,EAAE,YAAY,CAAC;IAEjC;;OAEG;IACK,aAAa,EAAE,YAAY,CAAC;IAEpC;;OAEG;IACK,eAAe,EAAE,YAAY,CAAC;IAE9B,cAAc,EAAE,mBAAmB,GAAG,IAAI,CAAC;IAEnD;;;;;;;OAOG;IACK,uBAAuB,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAErD,QAAgB,wBAAwB,CAAU;IAClD,QAAgB,SAAS,CAAU;IACnC,QAAgB,SAAS,CAAU;IAEnC;;OAEG;IACH,kBAAkB;IAwClB;;OAEG;IACH,IAAI;IAIJ;;;;OAIG;IACH,SAAS,CAAC,CAAC,EAAE,aAAa;IA8B1B;;;;;OAKG;IACH,OAAO,CAAC,CAAC,EAAE,aAAa;IAmBxB;;;OAGG;IACH,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG;QAAE,cAAc,EAAE,mBAAmB,CAAA;KAAE,EAAE,CAAC,EAAE,KAAK;IAuGtE;;OAEG;IACH,kBAAkB;IAIlB;;OAEG;IACH,gBAAgB;IAIhB,mBAAmB,CAAC,EAAE,MAAM,EAAE,EAAE,gBAAgB;IAOhD;;OAEG;IACH,IAAI;IAmBJ;;OAEG;IACH,KAAK;IAIL;;;;;;OAMG;IACH,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM;IAWnE;;;;;OAKG;IACH,mBAAmB,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM;IAyB/D;;;;;;OAMG;IACH,sBAAsB,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM;IAQlE;;;;OAIG;IACH,iBAAiB,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM;IAsB7D;;;OAGG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;IA+BhD;;;OAGG;IACH,cAAc,CAAC,CAAC,EAAE,aAAa;IAU/B;;;OAGG;IACH,YAAY,CAAC,CAAC,EAAE,aAAa;IAO7B;;;;OAIG;IACH,mBAAmB,CAAC,SAAS,EAAE,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,aAAa;IAuB9D;;;OAGG;IACH,mBAAmB,CAAC,MAAM,EAAE,MAAM;IAalC;;;OAGG;IACH,sBAAsB,CAAC,MAAM,EAAE,MAAM;IAWrC;;;OAGG;IACH,cAAc,CAAC,CAAC,EAAE,aAAa;IAO/B;;;;;OAKG;IACH,KAAK,CACH,CAAC,EAAE,aAAa,EAChB,IAAI,EAAE,gBAAgB,GAAG,cAAc,EACvC,SAAS,EAAE,MAAM,GAAG,OAAO,GAC1B,OAAO;IAiBV;;OAEG;IACH,SAAS,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,gBAAgB,GAAG,cAAc;IAInE;;OAEG;IACH,UAAU,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,gBAAgB,GAAG,cAAc;IAIpE;;;OAGG;IACH,0BAA0B,CAAC,CAAC,EAAE,aAAa;IAgB3C;;;OAGG;IACH,uBAAuB,CAAC,CAAC,EAAE,aAAa;IAYxC;;;OAGG;IACH,eAAe,CAAC,CAAC,EAAE,aAAa;IAUhC;;;;OAIG;IACH,sBAAsB,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC,EAAE,aAAa;IAepE;;;OAGG;IACH,wBAAwB,CAAC,CAAC,EAAE,aAAa;IAYzC;;;OAGG;IACH,2BAA2B,CAAC,CAAC,EAAE,aAAa;CAY7C"}