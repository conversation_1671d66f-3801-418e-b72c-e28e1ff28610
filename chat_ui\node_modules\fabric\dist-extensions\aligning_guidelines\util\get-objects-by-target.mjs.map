{"version": 3, "file": "get-objects-by-target.mjs", "sources": ["../../../extensions/aligning_guidelines/util/get-objects-by-target.ts"], "sourcesContent": ["import type { FabricObject } from 'fabric';\nimport { ActiveSelection, Group } from 'fabric';\n\nexport function getObjectsByTarget(target: FabricObject) {\n  const objects = new Set<FabricObject>();\n  const canvas = target.canvas;\n  if (!canvas) return objects;\n  const children =\n    target instanceof ActiveSelection ? target.getObjects() : [target];\n\n  canvas.forEachObject((o) => {\n    if (!o.isOnScreen()) return;\n    if (!o.visible) return;\n    if (o.constructor == Group) {\n      collectObjectsByGroup(objects, o);\n      return;\n    }\n    objects.add(o);\n  });\n\n  deleteObjectsByList(objects, children);\n  return objects;\n}\n\nfunction deleteObjectsByList(objects: Set<FabricObject>, list: FabricObject[]) {\n  for (const target of list) {\n    if (target.constructor == Group) {\n      deleteObjectsByList(objects, (target as Group).getObjects());\n    } else {\n      objects.delete(target);\n    }\n  }\n}\n\nfunction collectObjectsByGroup(objects: Set<FabricObject>, g: Group) {\n  const children = g.getObjects();\n  for (const child of children) {\n    if (!child.visible) continue;\n    if (child.constructor == Group) {\n      collectObjectsByGroup(objects, child);\n      continue;\n    }\n    objects.add(child);\n  }\n}\n"], "names": ["getObjectsByTarget", "target", "objects", "Set", "canvas", "children", "ActiveSelection", "getObjects", "forEachObject", "o", "isOnScreen", "visible", "constructor", "Group", "collectObjectsByGroup", "add", "deleteObjectsByList", "list", "delete", "g", "child"], "mappings": ";;AAGO,SAASA,kBAAkBA,CAACC,MAAoB,EAAE;AACvD,EAAA,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAgB,CAAA;AACvC,EAAA,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAM,CAAA;AAC5B,EAAA,IAAI,CAACA,MAAM,EAAE,OAAOF,OAAO,CAAA;AAC3B,EAAA,MAAMG,QAAQ,GACZJ,MAAM,YAAYK,eAAe,GAAGL,MAAM,CAACM,UAAU,EAAE,GAAG,CAACN,MAAM,CAAC,CAAA;AAEpEG,EAAAA,MAAM,CAACI,aAAa,CAAEC,CAAC,IAAK;AAC1B,IAAA,IAAI,CAACA,CAAC,CAACC,UAAU,EAAE,EAAE,OAAA;AACrB,IAAA,IAAI,CAACD,CAAC,CAACE,OAAO,EAAE,OAAA;AAChB,IAAA,IAAIF,CAAC,CAACG,WAAW,IAAIC,KAAK,EAAE;AAC1BC,MAAAA,qBAAqB,CAACZ,OAAO,EAAEO,CAAC,CAAC,CAAA;AACjC,MAAA,OAAA;AACF,KAAA;AACAP,IAAAA,OAAO,CAACa,GAAG,CAACN,CAAC,CAAC,CAAA;AAChB,GAAC,CAAC,CAAA;AAEFO,EAAAA,mBAAmB,CAACd,OAAO,EAAEG,QAAQ,CAAC,CAAA;AACtC,EAAA,OAAOH,OAAO,CAAA;AAChB,CAAA;AAEA,SAASc,mBAAmBA,CAACd,OAA0B,EAAEe,IAAoB,EAAE;AAC7E,EAAA,KAAK,MAAMhB,MAAM,IAAIgB,IAAI,EAAE;AACzB,IAAA,IAAIhB,MAAM,CAACW,WAAW,IAAIC,KAAK,EAAE;MAC/BG,mBAAmB,CAACd,OAAO,EAAGD,MAAM,CAAWM,UAAU,EAAE,CAAC,CAAA;AAC9D,KAAC,MAAM;AACLL,MAAAA,OAAO,CAACgB,MAAM,CAACjB,MAAM,CAAC,CAAA;AACxB,KAAA;AACF,GAAA;AACF,CAAA;AAEA,SAASa,qBAAqBA,CAACZ,OAA0B,EAAEiB,CAAQ,EAAE;AACnE,EAAA,MAAMd,QAAQ,GAAGc,CAAC,CAACZ,UAAU,EAAE,CAAA;AAC/B,EAAA,KAAK,MAAMa,KAAK,IAAIf,QAAQ,EAAE;AAC5B,IAAA,IAAI,CAACe,KAAK,CAACT,OAAO,EAAE,SAAA;AACpB,IAAA,IAAIS,KAAK,CAACR,WAAW,IAAIC,KAAK,EAAE;AAC9BC,MAAAA,qBAAqB,CAACZ,OAAO,EAAEkB,KAAK,CAAC,CAAA;AACrC,MAAA,SAAA;AACF,KAAA;AACAlB,IAAAA,OAAO,CAACa,GAAG,CAACK,KAAK,CAAC,CAAA;AACpB,GAAA;AACF;;;;"}