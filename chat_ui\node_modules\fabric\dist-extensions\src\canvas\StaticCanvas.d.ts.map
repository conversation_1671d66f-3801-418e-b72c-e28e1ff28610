{"version": 3, "file": "StaticCanvas.d.ts", "sourceRoot": "", "sources": ["../../../src/canvas/StaticCanvas.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAGzE,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEjD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,KAAK,EACV,SAAS,EAET,YAAY,EACZ,eAAe,EACf,OAAO,EACP,MAAM,EACN,KAAK,EACL,WAAW,EACX,uBAAuB,EACvB,oBAAoB,EACpB,QAAQ,EACT,MAAM,aAAa,CAAC;AASrB,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAStE,OAAO,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAC9E,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAKjE;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAC1B;IACE,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,OAAO,CAAC,EAAE,KAAK,CAAC;CACjB,GACD;IACE,aAAa,CAAC,EAAE,KAAK,CAAC;IACtB,OAAO,CAAC,EAAE,IAAI,CAAC;CAChB,CAAC;AAEN,MAAM,MAAM,iBAAiB,GAAG;IAC9B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,OAAO,CAAC,EAAE;QACR,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,OAAO,CAAC,EAAE,WAAW,CAAC;CACvB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEF;;;;;;;;GAQG;AAEH,qBAAa,YAAY,CAErB,SAAS,SAAS,kBAAkB,GAAG,kBAAkB,CAE3D,SAAQ,iBACR,YAAW,mBAAmB;IAEtB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IAGf,aAAa,EAAE,OAAO,CAAC;IACvB,eAAe,EAAE,OAAO,GAAG,MAAM,CAAC;IAClC,eAAe,CAAC,EAAE,YAAY,CAAC;IAE/B,UAAU,EAAE,OAAO,CAAC;IACpB,YAAY,EAAE,OAAO,GAAG,MAAM,CAAC;IAC/B,YAAY,CAAC,EAAE,YAAY,CAAC;IAE5B,QAAQ,CAAC,EAAE,YAAY,CAAC;IAExB,oBAAoB,EAAE,OAAO,CAAC;IAG9B,iBAAiB,EAAE,OAAO,CAAC;IAC3B,aAAa,EAAE,OAAO,CAAC;IACvB,mBAAmB,EAAE,OAAO,CAAC;IAC7B,qBAAqB,EAAE,OAAO,CAAC;IAEvC;;OAEG;IACK,oBAAoB,EAAE,OAAO,CAAC;IAEtC;;OAEG;IACK,mBAAmB,EAAE,OAAO,CAAC;IAE7B,iBAAiB,EAAE,MAAM,CAAC;IAElC;;OAEG;IACK,SAAS,EAAE,YAAY,CAAC;IAEhC;;;;OAIG;IACH,IAAI,aAAa,sBAEhB;IAED,IAAI,gBAAgB,6BAEnB;IAED;;;;OAIG;IACK,SAAS,CAAC,EAAE,OAAO,CAAC;IAE5B;;;;OAIG;IACK,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC;IAC/C,UAAkB,cAAc,EAAE,OAAO,CAAC;IAC1C,UAAkB,gBAAgB,EAAE,MAAM,CAAC;IAEnC,QAAQ,EAAE,sBAAsB,CAAC;IAEzC;;;;;;OAMG;IACH,UAAkB,mBAAmB,EAAE,OAAO,CAAC;IAE/C,MAAM,CAAC,WAAW,gCAAwB;IAG1C,UAAkB,aAAa,CAAC,EAAE;QAChC,IAAI,IAAI,CAAC;QACT,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;KAC9B,CAAC;IAEF,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;gBAKvC,EAAE,CAAC,EAAE,MAAM,GAAG,iBAAiB,EAC/B,OAAO,GAAE,QAAQ,CAAC,mBAAmB,CAAM;IAkB7C,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,iBAAiB;IAItD,GAAG,CAAC,GAAG,OAAO,EAAE,YAAY,EAAE;IAM9B,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,YAAY,EAAE;IAMlD,MAAM,CAAC,GAAG,OAAO,EAAE,YAAY,EAAE;IAMjC,cAAc,CAAC,GAAG,EAAE,YAAY;IAehC,gBAAgB,CAAC,GAAG,EAAE,YAAY;IAMlC,oBAAoB;IAIpB;;;;OAIG;IACH,gBAAgB;IAIhB;;;OAGG;IACH,UAAU;;;;IAIV;;;OAGG;IACH,QAAQ,IAAI,MAAM;IAIlB;;;OAGG;IACH,SAAS,IAAI,MAAM;IAInB;;;;;;;OAOG;IACH,QAAQ,CACN,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,EACrB,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,IAAI,CAAC;QAAC,OAAO,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IACP,QAAQ,CACN,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,EAC7B,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,IAAI,CAAC;QAAC,aAAa,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IAKP;;;;;;;OAOG;IACH,SAAS,CACP,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,EACtB,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,IAAI,CAAC;QAAC,OAAO,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IACP,SAAS,CACP,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,EAC9B,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,IAAI,CAAC;QAAC,aAAa,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IAKP;;;OAGG;IACH,SAAS,CAAC,kBAAkB,CAC1B,UAAU,EAAE,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,EAC1C,EAAE,OAAe,EAAE,aAAqB,EAAE,GAAE,kBAAuB;IAoBrE;;;;;;;;OAQG;IACH,aAAa,CACX,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,EAClC,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,IAAI,CAAC;QAAC,aAAa,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IACP,aAAa,CACX,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,EAC1B,OAAO,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,IAAI,CAAC;QAAC,OAAO,CAAC,EAAE,KAAK,CAAA;KAAE,GAClD,IAAI;IACP,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,IAAI;IAWhE;;;OAGG;IACH,OAAO;IAIP;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM;IAMhC;;;;;;;OAOG;IACH,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;IAavC;;;OAGG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM;IAIrB;;;OAGG;IACH,WAAW,CAAC,KAAK,EAAE,KAAK;IAOxB;;;OAGG;IACH,WAAW,CAAC,KAAK,EAAE,KAAK;IASxB;;;OAGG;IACH,UAAU,IAAI,iBAAiB;IAI/B;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,wBAAwB;IAI1C;;;OAGG;IACH,UAAU,IAAI,wBAAwB;IAItC;;OAEG;IACH,KAAK;IAWL;;OAEG;IACH,SAAS;IAQT;;;;;;;OAOG;IACH,cAAc;IAKd;;;;OAIG;IACH,gBAAgB;IAMhB;;;OAGG;IACH,sBAAsB,IAAI,YAAY;IAkBtC,qBAAqB;IAOrB,YAAY,CAAC,IAAI,EAAE,wBAAwB;IAI3C;;;;OAIG;IACH,YAAY,CAAC,GAAG,EAAE,wBAAwB,EAAE,OAAO,EAAE,YAAY,EAAE;IA4CnE;;;OAGG;IACH,oBAAoB,CAClB,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,mBAAmB;IAkB/B;;;;OAIG;IACH,cAAc,CAAC,GAAG,EAAE,wBAAwB,EAAE,OAAO,EAAE,YAAY,EAAE;IAMrE;;;;OAIG;IACH,0BAA0B,CACxB,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,YAAY,GAAG,SAAS;IA8CpC;;;OAGG;IACH,iBAAiB,CAAC,GAAG,EAAE,wBAAwB;IAI/C;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,wBAAwB;IAI5C;;;;;OAKG;IACH,SAAS;;;;IAOT;;;OAGG;IACH,cAAc;IAId;;OAEG;IACH,aAAa,CAAC,MAAM,EAAE,YAAY;IAOlC;;;OAGG;IACH,aAAa,CAAC,MAAM,EAAE,YAAY;IAOlC;;;OAGG;IACH,YAAY,CAAC,MAAM,EAAE,YAAY;IAIjC;;;OAGG;IACH,oBAAoB,CAAC,MAAM,EAAE,YAAY;IAIzC;;;OAGG;IACH,qBAAqB,CAAC,MAAM,EAAE,YAAY;IAO1C;;;OAGG;IACH,qBAAqB,CAAC,MAAM,EAAE,YAAY;IAO1C;;;OAGG;IACH,WAAW,IAAI,KAAK;IAOpB;;;;OAIG;IACH,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK;IAMjD;;;;OAIG;IACH,cAAc,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAI7C;;;;OAIG;IACH,QAAQ,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAIvC;;;;;;;;;;;;;;OAcG;IACH,MAAM;IAIN;;;;OAIG;IACH,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAI/C;;OAEG;IACH,eAAe,CACb,UAAU,EAAE,oBAAoB,EAChC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAoBhC;;OAEG;IACH,SAAS,CAAC,SAAS,CACjB,QAAQ,EAAE,YAAY,EACtB,UAAU,EAAE,oBAAoB,EAChC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAgBhC;;OAEG;IACH,oBAAoB,CAClB,UAAU,EAAE,oBAAoB,EAChC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IA4CxB,yBAAyB,EAAE,OAAO,CAAC;IAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,KAAK,CAAC,OAAO,GAAE,iBAAsB,EAAE,OAAO,CAAC,EAAE,WAAW;IAuB5D;;OAEG;IACH,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,iBAAiB,GAAG,IAAI;IAanE;;OAEG;IACH,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,iBAAiB,GAAG,IAAI;IA6CjE,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,GAAG,MAAM;IAW3D;;;OAGG;IACH,0BAA0B,IAAI,MAAM;IAqBpC;;;;;;OAMG;IACH,wBAAwB,IAAI,MAAM;IA8ClC;;OAEG;IACH,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE,WAAW;IAStD;;;OAGG;IACH,aAAa,CACX,MAAM,EAAE,MAAM,EAAE,EAChB,QAAQ,EAAE,YAAY,EACtB,OAAO,CAAC,EAAE,WAAW;IAKvB;;OAEG;IACH,qBAAqB,CACnB,MAAM,EAAE,MAAM,EAAE,EAChB,QAAQ,EAAE,cAAc,GAAG,iBAAiB,EAC5C,OAAO,CAAC,EAAE,WAAW;IAQvB;;;OAGG;IACH,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,YAAY,GAAG,SAAS;IAwC1E;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,YAAY,CACV,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAClC,OAAO,CAAC,EAAE,oBAAoB,CAAC,SAAS,CAAC,EACzC,EAAE,MAAM,EAAE,GAAE,SAAc,GACzB,OAAO,CAAC,IAAI,CAAC;IA2ChB;;;OAGG;IACH,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE;IAM1B;;;OAGG;IACH,gBAAgB;IAKhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACH,SAAS,CAAC,OAAO,GAAS,eAAe,GAAG,MAAM;IAgBlD,MAAM,CAAC,OAAO,GAAS,eAAe,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IAiB7D;;;;;;;;;;;;;OAaG;IACH,eAAe,CACb,UAAU,SAAI,EACd,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAS,uBAAuB,GACnE,iBAAiB;IAoCpB;;;;OAIG;IACH,OAAO;IAyBP;;;;;;;;;;;;;OAaG;IACH,OAAO;IAgBP;;;OAGG;IACH,QAAQ;CAKT"}