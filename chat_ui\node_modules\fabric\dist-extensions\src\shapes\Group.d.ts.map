{"version": 3, "file": "Group.d.ts", "sourceRoot": "", "sources": ["../../../src/shapes/Group.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAEvE,OAAO,KAAK,EACV,gBAAgB,EAChB,WAAW,EACX,QAAQ,EACR,SAAS,EACV,MAAM,aAAa,CAAC;AAUrB,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAGrD,OAAO,KAAK,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAE/E,OAAO,KAAK,EACV,uBAAuB,EACvB,iBAAiB,EACjB,gBAAgB,EACjB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAO/D,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;AAE9E,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAYnD,MAAM,WAAW,WAAY,SAAQ,YAAY,EAAE,gBAAgB;IACjE,eAAe,EAAE,iBAAiB,CAAC;IACnC,cAAc,EAAE,gBAAgB,CAAC;CAClC;AAED,MAAM,WAAW,aAAa;IAC5B,cAAc,EAAE,OAAO,CAAC;IACxB,WAAW,EAAE,OAAO,CAAC;CACtB;AAED,MAAM,WAAW,oBACf,SAAQ,qBAAqB,EAC3B,aAAa;IACf,OAAO,EAAE,qBAAqB,EAAE,CAAC;IACjC,aAAa,EAAE,uBAAuB,CAAC;CACxC;AAED,MAAM,WAAW,UAAW,SAAQ,iBAAiB,EAAE,aAAa;IAClE,aAAa,EAAE,aAAa,CAAC;CAC9B;AAED,eAAO,MAAM,kBAAkB,EAAE,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAI/D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kBAwoBsmsC,CAAC;;;;AAtoBzmsC;;;;;GAKG;AACH,qBAAa,KACX,SAAQ,UAGR,YAAW,UAAU;IAErB;;;;;OAKG;IACK,cAAc,EAAE,OAAO,CAAC;IAEhC;;;;;;;;;;;OAWG;IACK,WAAW,EAAE,OAAO,CAAC;IAErB,aAAa,EAAE,aAAa,CAAC;IAErC;;;;;OAKG;IACH,SAAS,CAAC,cAAc,EAAE,YAAY,EAAE,CAAM;IAE9C,MAAM,CAAC,IAAI,SAAW;IAEtB,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAsB;IAC7D,OAAO,CAAC,wBAAwB,CAAyC;IACzE,OAAO,CAAC,yBAAyB,CAA2C;IAE5E,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAOzC;;;;;OAKG;gBACS,OAAO,GAAE,YAAY,EAAO,EAAE,OAAO,GAAE,OAAO,CAAC,UAAU,CAAM;IAO3E;;;OAGG;IACH,SAAS,CAAC,SAAS,CACjB,OAAO,EAAE,YAAY,EAAE,EACvB,OAAO,EAAE;QACP,aAAa,CAAC,EAAE,aAAa,CAAC;QAC9B,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;IA+BH;;;;;OAKG;IACH,aAAa,CAAC,MAAM,EAAE,YAAY;IAmBlC;;;;OAIG;IACH,SAAS,CAAC,iCAAiC,CAAC,OAAO,EAAE,YAAY,EAAE;IAOnE;;;OAGG;IACH,GAAG,CAAC,GAAG,OAAO,EAAE,YAAY,EAAE;IAO9B;;;;OAIG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,YAAY,EAAE;IAOlD;;;;OAIG;IACH,MAAM,CAAC,GAAG,OAAO,EAAE,YAAY,EAAE;IAMjC,cAAc,CAAC,MAAM,EAAE,YAAY;IAMnC;;;;OAIG;IACH,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,qBAAqB,CAAC,EAAE,OAAO;IAMtE;;;;OAIG;IACH,qBAAqB,CAAC,IAAI,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE;IAQxE,oBAAoB;IAIpB;;;;OAIG;IACH,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAW5B;;OAEG;IACH,sBAAsB;IAItB;;;OAGG;IACH,SAAS;IAKT;;;OAGG;IACH,wBAAwB,CAAC,CAAC,SAAS,OAAO,EACxC,QAAQ,EAAE,CAAC,EACX,EACE,MAAM,EAAE,MAAM,GACf,EAAE,YAAY,CAAC,CAAC,SAAS,IAAI,GAAG,UAAU,GAAG,YAAY,CAAC;IAe7D;;;;OAIG;IACH,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY;IAYjD;;;;OAIG;IACH,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,qBAAqB,CAAC,EAAE,OAAO;IAMhE;;;;OAIG;IACH,WAAW,CAAC,MAAM,EAAE,YAAY,EAAE,qBAAqB,CAAC,EAAE,OAAO;IA4BjE;;;;OAIG;IACH,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE,qBAAqB,CAAC,EAAE,OAAO;IAM/D;;;;;;;;OAQG;IACH,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,qBAAqB,CAAC,EAAE,OAAO;IAoBhE;;;;;;OAMG;IACH,WAAW;IAaX;;;OAGG;IACH,cAAc;IAYd;;;OAGG;IACH,UAAU,IAAI,OAAO;IAIrB;;;OAGG;IACH,UAAU,CACR,GAAG,EAAE,wBAAwB,EAC7B,WAAW,EAAE,OAAO,GAAG,SAAS,EAChC,OAAO,EAAE,WAAW;IAkBtB;;;OAGG;IACH,SAAS;IAMT,aAAa,CAAC,OAAO,GAAE,uBAA4B;IAQnD;;;OAGG;IACH,MAAM,CAAC,GAAG,EAAE,wBAAwB;IAMpC;;;;;;OAMG;IACH,kBAAkB,CAChB,MAAM,EAAE,UAAU,GAAG,kBAAkB,EACvC,mBAAmB,CAAC,EAAE,MAAM,EAAE;IAiBhC;;;;OAIG;IACH,QAAQ,CACN,CAAC,SAAS,IAAI,CACZ,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,EACnC,MAAM,oBAAoB,CAC3B,EACD,CAAC,SAAS,MAAM,CAAC,GAAG,KAAK,EACzB,mBAAmB,GAAE,CAAC,EAAO,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,oBAAoB;IAmBnE,QAAQ;IAIR,OAAO;IAaP;;OAEG;IACH,gBAAgB,CAAC,OAAO,CAAC,EAAE,WAAW;IAWtC;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,WAAW;IAW5B;;;OAGG;IACH,YAAY,IAAI,MAAM;IAStB;;;;OAIG;IACH,aAAa,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,MAAM;IAY5C;;;;;;;OAOG;IACH,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,QAAQ,CAAC,oBAAoB,CAAC,EACxD,EAAE,IAAI,EAAE,OAAY,EAAE,aAAa,EAAE,GAAG,OAAO,EAAE,EAAE,CAAC,EACpD,SAAS,CAAC,EAAE,SAAS;CA+BxB"}