'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import * as fabric from 'fabric';

// Define the shape of the context state
interface ImageEditorContextType {
  canvas: fabric.Canvas | null;
  setCanvas: (canvas: fabric.Canvas | null) => void;
}

// Create the context with a default value
const ImageEditorContext = createContext<ImageEditorContextType | null>(null);

// Create a provider component
interface ImageEditorProviderProps {
  children: ReactNode;
}

export const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);

  return (
    <ImageEditorContext.Provider value={{ canvas, setCanvas }}>
      {children}
    </ImageEditorContext.Provider>
  );
};

// Create a custom hook to use the context
export const useImageEditor = () => {
  const context = useContext(ImageEditorContext);
  if (!context) {
    throw new Error('useImageEditor must be used within an ImageEditorProvider');
  }
  return context;
};
