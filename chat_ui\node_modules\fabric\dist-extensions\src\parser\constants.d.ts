export declare const reNum: string;
export declare const viewportSeparator: string;
export declare const svgNS = "http://www.w3.org/2000/svg";
export declare const reFontDeclaration: RegExp;
export declare const svgValidTagNames: string[], svgViewBoxElements: string[], svgInvalidAncestors: string[], svgValidParents: string[], attributesMap: {
    cx: string;
    x: string;
    r: string;
    cy: string;
    y: string;
    display: string;
    visibility: string;
    transform: string;
    'fill-opacity': string;
    'fill-rule': string;
    'font-family': string;
    'font-size': string;
    'font-style': string;
    'font-weight': string;
    'letter-spacing': string;
    'paint-order': string;
    'stroke-dasharray': string;
    'stroke-dashoffset': string;
    'stroke-linecap': string;
    'stroke-linejoin': string;
    'stroke-miterlimit': string;
    'stroke-opacity': string;
    'stroke-width': string;
    'text-decoration': string;
    'text-anchor': string;
    opacity: string;
    'clip-path': string;
    'clip-rule': string;
    'vector-effect': string;
    'image-rendering': string;
    'text-decoration-thickness': string;
}, fSize = "font-size", cPath = "clip-path";
export declare const svgValidTagNamesRegEx: RegExp;
export declare const svgViewBoxElementsRegEx: RegExp;
export declare const svgValidParentsRegEx: RegExp;
export declare const reViewBoxAttrValue: RegExp;
//# sourceMappingURL=constants.d.ts.map