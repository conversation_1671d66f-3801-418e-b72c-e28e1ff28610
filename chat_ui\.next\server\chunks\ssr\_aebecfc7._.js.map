{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\nimport * as fabric from 'fabric';\r\n\r\nexport type Tool = 'select' | 'rectangle' | 'circle' | 'text' | 'brush' | 'eraser' | 'image';\r\n\r\n// Define the shape of the context state\r\ninterface ImageEditorContextType {\r\n  canvas: fabric.Canvas | null;\r\n  setCanvas: (canvas: fabric.Canvas | null) => void;\r\n  activeTool: Tool;\r\n  setActiveTool: (tool: Tool) => void;\r\n  brushSize: number;\r\n  setBrushSize: (size: number) => void;\r\n  brushColor: string;\r\n  setBrushColor: (color: string) => void;\r\n  fillColor: string;\r\n  setFillColor: (color: string) => void;\r\n  strokeColor: string;\r\n  setStrokeColor: (color: string) => void;\r\n  strokeWidth: number;\r\n  setStrokeWidth: (width: number) => void;\r\n  isDrawing: boolean;\r\n  setIsDrawing: (drawing: boolean) => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ImageEditorContext = createContext<ImageEditorContextType | null>(null);\r\n\r\n// Create a provider component\r\ninterface ImageEditorProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {\r\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\r\n  const [activeTool, setActiveTool] = useState<Tool>('select');\r\n  const [brushSize, setBrushSize] = useState(5);\r\n  const [brushColor, setBrushColor] = useState('#000000');\r\n  const [fillColor, setFillColor] = useState('#ff0000');\r\n  const [strokeColor, setStrokeColor] = useState('#000000');\r\n  const [strokeWidth, setStrokeWidth] = useState(2);\r\n  const [isDrawing, setIsDrawing] = useState(false);\r\n\r\n  return (\r\n    <ImageEditorContext.Provider value={{\r\n      canvas,\r\n      setCanvas,\r\n      activeTool,\r\n      setActiveTool,\r\n      brushSize,\r\n      setBrushSize,\r\n      brushColor,\r\n      setBrushColor,\r\n      fillColor,\r\n      setFillColor,\r\n      strokeColor,\r\n      setStrokeColor,\r\n      strokeWidth,\r\n      setStrokeWidth,\r\n      isDrawing,\r\n      setIsDrawing\r\n    }}>\r\n      {children}\r\n    </ImageEditorContext.Provider>\r\n  );\r\n};\r\n\r\n// Create a custom hook to use the context\r\nexport const useImageEditor = () => {\r\n  const context = useContext(ImageEditorContext);\r\n  if (!context) {\r\n    throw new Error('useImageEditor must be used within an ImageEditorProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA2BA,0CAA0C;AAC1C,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAOjE,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA4B;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAGO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Canvas.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useCallback } from 'react';\r\nimport * as fabric from 'fabric';\r\nimport { useImageEditor } from './context';\r\n\r\ninterface CanvasProps {\r\n  width: number;\r\n  height: number;\r\n}\r\n\r\nconst Canvas = ({ width, height }: CanvasProps) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const {\r\n    setCanvas,\r\n    activeTool,\r\n    brushSize,\r\n    brushColor,\r\n    fillColor,\r\n    strokeColor,\r\n    strokeWidth\r\n  } = useImageEditor();\r\n\r\n  const setupCanvasEvents = useCallback((canvas: fabric.Canvas) => {\r\n    // Drawing mode setup\r\n    if (activeTool === 'brush') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        canvas.freeDrawingBrush.color = brushColor;\r\n      }\r\n    } else {\r\n      canvas.isDrawingMode = false;\r\n    }\r\n\r\n    // Handle mouse events for shape creation\r\n    let isDown = false;\r\n    let origX = 0;\r\n    let origY = 0;\r\n    let shape: fabric.Object | null = null;\r\n\r\n    const onMouseDown = (o: any) => {\r\n      if (activeTool === 'select' || activeTool === 'brush') return;\r\n\r\n      isDown = true;\r\n      const pointer = canvas.getPointer(o.e);\r\n      origX = pointer.x;\r\n      origY = pointer.y;\r\n\r\n      if (activeTool === 'rectangle') {\r\n        shape = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: fillColor,\r\n          stroke: strokeColor,\r\n          strokeWidth: strokeWidth,\r\n          selectable: false,\r\n        });\r\n      } else if (activeTool === 'circle') {\r\n        shape = new fabric.Circle({\r\n          left: origX,\r\n          top: origY,\r\n          radius: 0,\r\n          fill: fillColor,\r\n          stroke: strokeColor,\r\n          strokeWidth: strokeWidth,\r\n          selectable: false,\r\n        });\r\n      }\r\n\r\n      if (shape) {\r\n        canvas.add(shape);\r\n      }\r\n    };\r\n\r\n    const onMouseMove = (o: any) => {\r\n      if (!isDown || !shape) return;\r\n\r\n      const pointer = canvas.getPointer(o.e);\r\n\r\n      if (activeTool === 'rectangle') {\r\n        const rect = shape as fabric.Rect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (activeTool === 'circle') {\r\n        const circle = shape as fabric.Circle;\r\n        const radius = Math.sqrt(Math.pow(pointer.x - origX, 2) + Math.pow(pointer.y - origY, 2)) / 2;\r\n        circle.set({ radius });\r\n      }\r\n\r\n      canvas.renderAll();\r\n    };\r\n\r\n    const onMouseUp = () => {\r\n      if (shape) {\r\n        shape.set({ selectable: true });\r\n        shape = null;\r\n      }\r\n      isDown = false;\r\n    };\r\n\r\n    canvas.on('mouse:down', onMouseDown);\r\n    canvas.on('mouse:move', onMouseMove);\r\n    canvas.on('mouse:up', onMouseUp);\r\n\r\n    return () => {\r\n      canvas.off('mouse:down', onMouseDown);\r\n      canvas.off('mouse:move', onMouseMove);\r\n      canvas.off('mouse:up', onMouseUp);\r\n    };\r\n  }, [activeTool, brushSize, brushColor, fillColor, strokeColor, strokeWidth]);\r\n\r\n  useEffect(() => {\r\n    if (canvasRef.current) {\r\n      const canvas = new fabric.Canvas(canvasRef.current, {\r\n        width,\r\n        height,\r\n        backgroundColor: 'transparent',\r\n      });\r\n\r\n      setCanvas(canvas);\r\n      const cleanup = setupCanvasEvents(canvas);\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        cleanup();\r\n        canvas.dispose();\r\n      };\r\n    }\r\n  }, [width, height, setCanvas, setupCanvasEvents]);\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Glassy background with grid pattern */}\r\n      <div\r\n        className=\"absolute inset-0 rounded-lg backdrop-blur-sm bg-white/10 border border-white/20 shadow-lg\"\r\n        style={{\r\n          width,\r\n          height,\r\n          backgroundImage: `\r\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\r\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\r\n          `,\r\n          backgroundSize: '20px 20px',\r\n        }}\r\n      />\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"relative z-10 rounded-lg\"\r\n        style={{ display: 'block' }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Canvas;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,SAAS,CAAC,EAAE,KAAK,EAAE,MAAM,EAAe;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EACJ,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACZ,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,qBAAqB;QACrB,IAAI,eAAe,SAAS;YAC1B,OAAO,aAAa,GAAG;YACvB,IAAI,OAAO,gBAAgB,EAAE;gBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAChC,OAAO,gBAAgB,CAAC,KAAK,GAAG;YAClC;QACF,OAAO;YACL,OAAO,aAAa,GAAG;QACzB;QAEA,yCAAyC;QACzC,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAI,QAAQ;QACZ,IAAI,QAA8B;QAElC,MAAM,cAAc,CAAC;YACnB,IAAI,eAAe,YAAY,eAAe,SAAS;YAEvD,SAAS;YACT,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YACrC,QAAQ,QAAQ,CAAC;YACjB,QAAQ,QAAQ,CAAC;YAEjB,IAAI,eAAe,aAAa;gBAC9B,QAAQ,IAAI,+IAAA,CAAA,OAAW,CAAC;oBACtB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,YAAY;gBACd;YACF,OAAO,IAAI,eAAe,UAAU;gBAClC,QAAQ,IAAI,+IAAA,CAAA,SAAa,CAAC;oBACxB,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,YAAY;gBACd;YACF;YAEA,IAAI,OAAO;gBACT,OAAO,GAAG,CAAC;YACb;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO;YAEvB,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YAErC,IAAI,eAAe,aAAa;gBAC9B,MAAM,OAAO;gBACb,KAAK,GAAG,CAAC;oBACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;oBAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;gBAC/B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,MAAM,QAAQ,CAAC;oBAAC;gBAC7B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,KAAK,QAAQ,CAAC;oBAAC;gBAC5B;YACF,OAAO,IAAI,eAAe,UAAU;gBAClC,MAAM,SAAS;gBACf,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,MAAM;gBAC5F,OAAO,GAAG,CAAC;oBAAE;gBAAO;YACtB;YAEA,OAAO,SAAS;QAClB;QAEA,MAAM,YAAY;YAChB,IAAI,OAAO;gBACT,MAAM,GAAG,CAAC;oBAAE,YAAY;gBAAK;gBAC7B,QAAQ;YACV;YACA,SAAS;QACX;QAEA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,YAAY;QAEtB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,YAAY;QACzB;IACF,GAAG;QAAC;QAAY;QAAW;QAAY;QAAW;QAAa;KAAY;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,SAAS,IAAI,+IAAA,CAAA,SAAa,CAAC,UAAU,OAAO,EAAE;gBAClD;gBACA;gBACA,iBAAiB;YACnB;YAEA,UAAU;YACV,MAAM,UAAU,kBAAkB;YAElC,sBAAsB;YACtB,OAAO;gBACL;gBACA,OAAO,OAAO;YAChB;QACF;IACF,GAAG;QAAC;QAAO;QAAQ;QAAW;KAAkB;IAEhD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL;oBACA;oBACA,iBAAiB,CAAC;;;UAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAEF,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAQ;;;;;;;;;;;;AAIlC;uCAEe", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useImageEditor } from './context';\r\nimport * as fabric from 'fabric';\r\n\r\nconst Toolbar = () => {\r\n  const { canvas } = useImageEditor();\r\n\r\n  const addRectangle = () => {\r\n    if (canvas) {\r\n      const rect = new fabric.Rect({\r\n        left: 100,\r\n        top: 100,\r\n        fill: 'red',\r\n        width: 200,\r\n        height: 100,\r\n      });\r\n      canvas.add(rect);\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ marginBottom: '10px' }}>\r\n      <button onClick={addRectangle}>Add Rectangle</button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Toolbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEhC,MAAM,eAAe;QACnB,IAAI,QAAQ;YACV,MAAM,OAAO,IAAI,+IAAA,CAAA,OAAW,CAAC;gBAC3B,MAAM;gBACN,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,QAAQ;YACV;YACA,OAAO,GAAG,CAAC;YACX,OAAO,SAAS;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,OAAO;YAAE,cAAc;QAAO;kBACjC,cAAA,8OAAC;YAAO,SAAS;sBAAc;;;;;;;;;;;AAGrC;uCAEe", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ImageEditorProvider } from './context';\r\nimport Canvas from './Canvas';\r\nimport Toolbar from './Toolbar';\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ImageEditorProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ImageEditor = ({ children }: ImageEditorProps) => {\r\n  return <ImageEditorProvider>{children}</ImageEditorProvider>;\r\n};\r\n\r\nImageEditor.Canvas = Canvas;\r\nImageEditor.Toolbar = Toolbar;\r\n\r\nexport default ImageEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAoB;IACjD,qBAAO,8OAAC,+IAAA,CAAA,sBAAmB;kBAAE;;;;;;AAC/B;AAEA,YAAY,MAAM,GAAG,8IAAA,CAAA,UAAM;AAC3B,YAAY,OAAO,GAAG,+IAAA,CAAA,UAAO;uCAEd", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/design/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ImageEditor from '../../../../components/design/ImageEditor';\r\n\r\nexport default function DesignPage() {\r\n  return (\r\n    <div className='flex flex-col items-center justify-center'>\r\n      <ImageEditor>\r\n        <ImageEditor.Toolbar />\r\n        <ImageEditor.Canvas width={800} height={600} />\r\n      </ImageEditor>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,6IAAA,CAAA,UAAW;;8BACV,8OAAC,6IAAA,CAAA,UAAW,CAAC,OAAO;;;;;8BACpB,8OAAC,6IAAA,CAAA,UAAW,CAAC,MAAM;oBAAC,OAAO;oBAAK,QAAQ;;;;;;;;;;;;;;;;;AAIhD", "debugId": null}}]}