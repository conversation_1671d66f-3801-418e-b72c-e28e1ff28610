{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\nimport * as fabric from 'fabric';\r\n\r\n// Define the shape of the context state\r\ninterface ImageEditorContextType {\r\n  canvas: fabric.Canvas | null;\r\n  setCanvas: (canvas: fabric.Canvas | null) => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ImageEditorContext = createContext<ImageEditorContextType | null>(null);\r\n\r\n// Create a provider component\r\ninterface ImageEditorProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {\r\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\r\n\r\n  return (\r\n    <ImageEditorContext.Provider value={{ canvas, setCanvas }}>\r\n      {children}\r\n    </ImageEditorContext.Provider>\r\n  );\r\n};\r\n\r\n// Create a custom hook to use the context\r\nexport const useImageEditor = () => {\r\n  const context = useContext(ImageEditorContext);\r\n  if (!context) {\r\n    throw new Error('useImageEditor must be used within an ImageEditorProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,0CAA0C;AAC1C,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAOjE,MAAM,sBAAsB;QAAC,EAAE,QAAQ,EAA4B;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAE3D,qBACE,6LAAC,mBAAmB,QAAQ;QAAC,OAAO;YAAE;YAAQ;QAAU;kBACrD;;;;;;AAGP;GARa;KAAA;AAWN,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Canvas.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef } from 'react';\r\nimport * as fabric from 'fabric';\r\nimport { useImageEditor } from './context';\r\n\r\ninterface CanvasProps {\r\n  width: number;\r\n  height: number;\r\n}\r\n\r\nconst Canvas = ({ width, height }: CanvasProps) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const { setCanvas } = useImageEditor();\r\n\r\n  useEffect(() => {\r\n    if (canvasRef.current) {\r\n      const canvas = new fabric.Canvas(canvasRef.current, {\r\n        width,\r\n        height,\r\n        backgroundColor: '#f0f0f0',\r\n      });\r\n      setCanvas(canvas);\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        canvas.dispose();\r\n      };\r\n    }\r\n  }, [width, height, setCanvas]);\r\n\r\n  return <canvas ref={canvasRef} />;\r\n};\r\n\r\nexport default Canvas;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWA,MAAM,SAAS;QAAC,EAAE,KAAK,EAAE,MAAM,EAAe;;IAC5C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,SAAS,IAAI,kJAAA,CAAA,SAAa,CAAC,UAAU,OAAO,EAAE;oBAClD;oBACA;oBACA,iBAAiB;gBACnB;gBACA,UAAU;gBAEV,sBAAsB;gBACtB;wCAAO;wBACL,OAAO,OAAO;oBAChB;;YACF;QACF;2BAAG;QAAC;QAAO;QAAQ;KAAU;IAE7B,qBAAO,6LAAC;QAAO,KAAK;;;;;;AACtB;GArBM;;QAEkB,kJAAA,CAAA,iBAAc;;;KAFhC;uCAuBS", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useImageEditor } from './context';\r\nimport * as fabric from 'fabric';\r\n\r\nconst Toolbar = () => {\r\n  const { canvas } = useImageEditor();\r\n\r\n  const addRectangle = () => {\r\n    if (canvas) {\r\n      const rect = new fabric.Rect({\r\n        left: 100,\r\n        top: 100,\r\n        fill: 'red',\r\n        width: 200,\r\n        height: 100,\r\n      });\r\n      canvas.add(rect);\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ marginBottom: '10px' }}>\r\n      <button onClick={addRectangle}>Add Rectangle</button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Toolbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEhC,MAAM,eAAe;QACnB,IAAI,QAAQ;YACV,MAAM,OAAO,IAAI,kJAAA,CAAA,OAAW,CAAC;gBAC3B,MAAM;gBACN,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,QAAQ;YACV;YACA,OAAO,GAAG,CAAC;YACX,OAAO,SAAS;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,cAAc;QAAO;kBACjC,cAAA,6LAAC;YAAO,SAAS;sBAAc;;;;;;;;;;;AAGrC;GAtBM;;QACe,kJAAA,CAAA,iBAAc;;;KAD7B;uCAwBS", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ImageEditorProvider } from './context';\r\nimport Canvas from './Canvas';\r\nimport Toolbar from './Toolbar';\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ImageEditorProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ImageEditor = ({ children }: ImageEditorProps) => {\r\n  return <ImageEditorProvider>{children}</ImageEditorProvider>;\r\n};\r\n\r\nImageEditor.Canvas = Canvas;\r\nImageEditor.Toolbar = Toolbar;\r\n\r\nexport default ImageEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAoB;IACjD,qBAAO,6LAAC,kJAAA,CAAA,sBAAmB;kBAAE;;;;;;AAC/B;KAFM;AAIN,YAAY,MAAM,GAAG,iJAAA,CAAA,UAAM;AAC3B,YAAY,OAAO,GAAG,kJAAA,CAAA,UAAO;uCAEd", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/design/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ImageEditor from '../../../../components/design/ImageEditor';\r\n\r\nexport default function DesignPage() {\r\n  return (\r\n    <div className='flex flex-col items-center justify-center'>\r\n      <ImageEditor>\r\n        <ImageEditor.Toolbar />\r\n        <ImageEditor.Canvas width={800} height={600} />\r\n      </ImageEditor>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,gJAAA,CAAA,UAAW;;8BACV,6LAAC,gJAAA,CAAA,UAAW,CAAC,OAAO;;;;;8BACpB,6LAAC,gJAAA,CAAA,UAAW,CAAC,MAAM;oBAAC,OAAO;oBAAK,QAAQ;;;;;;;;;;;;;;;;;AAIhD;KATwB", "debugId": null}}]}