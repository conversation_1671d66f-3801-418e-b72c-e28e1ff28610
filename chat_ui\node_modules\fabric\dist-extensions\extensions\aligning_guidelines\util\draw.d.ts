import type { Canvas } from 'fabric';
import type { HorizontalLine, VerticalLine } from '../typedefs';
export declare function drawPointList(canvas: Canvas, list: Array<VerticalLine | HorizontalLine>): void;
export declare function drawVerticalLine(canvas: Canvas, coords: VerticalLine): void;
export declare function drawHorizontalLine(canvas: Canvas, coords: HorizontalLine): void;
//# sourceMappingURL=draw.d.ts.map