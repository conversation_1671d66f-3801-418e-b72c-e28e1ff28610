{"version": 3, "file": "ObjectGeometry.d.ts", "sourceRoot": "", "sources": ["../../../../src/shapes/Object/ObjectGeometry.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,KAAK,EACL,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,QAAQ,EACT,MAAM,gBAAgB,CAAC;AAGxB,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAYpC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAEzD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAItC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACpD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAG/D,KAAK,YAAY,GAAG;IAClB,GAAG,EAAE,MAAM,EAAE,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,KAAK,QAAQ,GAAG,YAAY,CAAC;AAE7B,qBAAa,cAAc,CAAC,SAAS,SAAS,YAAY,GAAG,YAAY,CACvE,SAAQ,aAAa,CAAC,SAAS,CAC/B,YACE,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,EAC7B,SAAS,EACT,IAAI,CAAC,eAAe,EAAE,aAAa,GAAG,eAAe,CAAC;IAIhD,OAAO,EAAE,MAAM,CAAC;IAExB;;;;;;;OAOG;IACK,OAAO,EAAE,QAAQ,CAAC;IAE1B;;OAEG;IACK,cAAc,CAAC,EAAE,YAAY,CAAC;IAEtC;;OAEG;IACK,WAAW,CAAC,EAAE,YAAY,CAAC;IAEnC;;;;;OAKG;IACK,MAAM,CAAC,EAAE,YAAY,GAAG,MAAM,CAAC;IAEvC;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;OAEG;IACH,IAAI,CAAC,KAAK,EAAE,MAAM;IAIlB;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;OAEG;IACH,IAAI,CAAC,KAAK,EAAE,MAAM;IAIlB;;;OAGG;IACH,YAAY,IAAI,MAAM;IAItB;;;OAGG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM;IAI1B;;;OAGG;IACH,YAAY,IAAI,MAAM;IAItB;;;OAGG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM;IAI1B;;OAEG;IACH,KAAK,IAAI,KAAK;IAOd;;;;;;;;;OASG;IACH,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,QAAQ;IAU1D;;OAEG;IACH,aAAa,IAAI,KAAK;IAItB;;;;;OAKG;IACH,aAAa,CACX,KAAK,EAAE,KAAK,EACZ,OAAO,GAAE,QAAuB,EAChC,OAAO,GAAE,QAAuB;IAKlC;;OAEG;IACH,SAAS,CAAC,gCAAgC;IAI1C;;OAEG;IACH,SAAS,IAAI,KAAK,EAAE;IAWpB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,GAAG,OAAO;IASjD;;;;OAIG;IACH,oBAAoB,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAcpD;;;;OAIG;IACH,uBAAuB,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAKvD;;OAEG;IACH,qBAAqB,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,GAAG,OAAO;IAUpD,aAAa,CAAC,CAAC,SAAS,cAAc,EAAE,KAAK,EAAE,CAAC,GAAG,OAAO;IAQ1D;;;;OAIG;IACH,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAIpC;;;;OAIG;IACH,UAAU,IAAI,OAAO;IA0BrB;;;OAGG;IACH,mBAAmB,IAAI,OAAO;IAiB9B;;;;OAIG;IACH,eAAe,IAAI,KAAK;IAIxB;;;;OAIG;IACH,cAAc,IAAI,MAAM;IAIxB;;;;OAIG;IACH,eAAe,IAAI,MAAM;IAIzB;;;;OAIG;IACH,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM1B;;;;OAIG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM;IAO1B;;;;OAIG;IACH,aAAa,CAAC,KAAK,EAAE,MAAM;IAO3B,sBAAsB;IAItB;;;OAGG;IACH,aAAa,IAAI,OAAO;IAMxB;;;OAGG;IACH,oBAAoB,IAAI,MAAM;IAI9B;;;;OAIG;IACH,WAAW,IAAI,YAAY;IAiB3B;;;;OAIG;IACH,SAAS,IAAI,IAAI;IAIjB,kBAAkB,CAAC,SAAS,UAAQ,GAAG,MAAM,EAAE;IAyB/C;;;;;;OAMG;IACH,mBAAmB,CAAC,SAAS,UAAQ,GAAG,MAAM;IAuB9C;;;;OAIG;IACH,aAAa,IAAI,MAAM;IA0BvB;;;;OAIG;IACH,4BAA4B,IAAI,KAAK;IAIrC;;;;;;OAMG;IACH,2BAA2B,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,KAAK;IAQzC,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;IACtB;;SAEK;IACG,OAAO,EAAE,QAAQ,CAAC;IAC1B;;SAEK;IACG,OAAO,EAAE,QAAQ,CAAC;IAClB,KAAK,EAAE,OAAO,CAAC;IACf,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,OAAO,CAAC;IAE/B;;;OAGG;IACK,KAAK,CAAC,EAAE,KAAK,CAAC;IAEtB;;;;;;;;;;OAUG;IACH,yBAAyB,CAAC,OAAO,GAAE,GAAQ,GAAG,KAAK;IA4CnD;;;;;;;;OAQG;IACH,sBAAsB,CACpB,KAAK,EAAE,KAAK,EACZ,WAAW,EAAE,QAAQ,EACrB,WAAW,EAAE,QAAQ,EACrB,SAAS,EAAE,QAAQ,EACnB,SAAS,EAAE,QAAQ,GAClB,KAAK;IAeR;;;;;;OAMG;IACH,sBAAsB,CACpB,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,QAAQ,EACjB,OAAO,EAAE,QAAQ,GAChB,KAAK;IAiBR;;;;;;OAMG;IACH,sBAAsB,CACpB,MAAM,EAAE,KAAK,EACb,OAAO,EAAE,QAAQ,EACjB,OAAO,EAAE,QAAQ,GAChB,KAAK;IAcR;;;OAGG;IACH,cAAc,IAAI,KAAK;IAOvB;;;OAGG;IACH,sBAAsB,IAAI,KAAK;IAQ/B;;;;;;;;;;OAUG;IACH,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,GAAG,KAAK;IAQ7D;;;;;;OAMG;IACH,mBAAmB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;IAUpE;;OAEG;IACH,iBAAiB;CAOlB"}