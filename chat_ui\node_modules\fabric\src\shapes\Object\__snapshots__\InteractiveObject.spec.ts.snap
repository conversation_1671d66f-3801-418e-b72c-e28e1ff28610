// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InteractiveObject Interactive + BaseObject default values 1`] = `
{
  "absolutePositioned": false,
  "activeOn": "down",
  "angle": 0,
  "backgroundColor": "",
  "borderColor": "rgb(178,204,255)",
  "borderDashArray": null,
  "borderOpacityWhenMoving": 0.4,
  "borderScaleFactor": 1,
  "centeredRotation": true,
  "centeredScaling": false,
  "clipPath": undefined,
  "cornerColor": "rgb(178,204,255)",
  "cornerDashArray": null,
  "cornerSize": 13,
  "cornerStrokeColor": "",
  "cornerStyle": "rect",
  "dirty": true,
  "evented": true,
  "excludeFromExport": false,
  "fill": "rgb(0,0,0)",
  "fillRule": "nonzero",
  "flipX": false,
  "flipY": false,
  "globalCompositeOperation": "source-over",
  "hasBorders": true,
  "hasControls": true,
  "height": 0,
  "hoverCursor": null,
  "includeDefaultValues": true,
  "inverted": false,
  "left": 0,
  "lockMovementX": false,
  "lockMovementY": false,
  "lockRotation": false,
  "lockScalingFlip": false,
  "lockScalingX": false,
  "lockScalingY": false,
  "lockSkewingX": false,
  "lockSkewingY": false,
  "minScaleLimit": 0,
  "moveCursor": null,
  "noScaleCache": true,
  "objectCaching": true,
  "opacity": 1,
  "originX": "left",
  "originY": "top",
  "padding": 0,
  "paintFirst": "fill",
  "perPixelTargetFind": false,
  "scaleX": 1,
  "scaleY": 1,
  "selectable": true,
  "selectionBackgroundColor": "",
  "shadow": null,
  "skewX": 0,
  "skewY": 0,
  "stroke": null,
  "strokeDashArray": null,
  "strokeDashOffset": 0,
  "strokeLineCap": "butt",
  "strokeLineJoin": "miter",
  "strokeMiterLimit": 4,
  "strokeUniform": false,
  "strokeWidth": 1,
  "top": 0,
  "touchCornerSize": 24,
  "transparentCorners": true,
  "visible": true,
  "width": 0,
}
`;
