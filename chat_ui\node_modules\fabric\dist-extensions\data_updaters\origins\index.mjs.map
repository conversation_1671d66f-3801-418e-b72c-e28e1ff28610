{"version": 3, "file": "index.mjs", "sources": ["../../../extensions/data_updaters/origins/index.ts"], "sourcesContent": ["import {\n  Point,\n  FabricImage,\n  Group,\n  BaseFabricObject,\n  type FabricObject,\n  type TOriginX,\n  type TOriginY,\n} from 'fabric';\n\n/**\n * Updates the fromObject function of a class to return a version that can restore old data\n * with values of originX and originY that are different from 'center', 'center'\n * Used to upgrade from fabric 6 to fabric 7\n * @param originalFn the original fromObject function of an object,\n * @param defaultOriginX optional default value for non exported originX,\n * @param defaultOriginY optional default value for non exported originY,\n * @returns a wrapped fromObject function for the object\n */\nexport const originUpdaterWrapper = <T extends FabricObject = FabricObject>(\n  originalFn: (...args: any[]) => Promise<T>,\n  defaultOriginX: TOriginX = 'left',\n  defaultOriginY: TOriginY = 'top',\n): ((...args: any[]) => Promise<T>) =>\n  async function (this: T, serializedObject, ...args) {\n    // we default to left and top because those are defaults before deprecation\n    const { originX = defaultOriginX, originY = defaultOriginY } =\n      serializedObject;\n    // and we do not want to pass those properties on the object anymore\n    delete serializedObject.originX;\n    delete serializedObject.originY;\n    const originalObject = await originalFn.call(\n      this,\n      serializedObject,\n      ...args,\n    );\n    const actualPosition = new Point(originalObject.left, originalObject.top);\n    originalObject.setPositionByOrigin(actualPosition, originX, originY);\n    return originalObject;\n  };\n\n/**\n * Wraps and override the current fabricJS fromObject static functions\n * Used to upgrade from fabric 6 to fabric 7\n * @param defaultOriginX optional default value for non exported originX,\n * @param defaultOriginY optional default value for non exported originY,\n * @returns a wrapped fromObject function for the object\n */\nexport const installOriginWrapperUpdater = (\n  originX?: TOriginX,\n  originY?: TOriginY,\n) => {\n  // @ts-expect-error the _fromObject parameter could be instantiated differently\n  BaseFabricObject._fromObject = originUpdaterWrapper<FabricObject>(\n    BaseFabricObject._fromObject,\n    originX,\n    originY,\n  );\n  // FabricImage and Group do not use _fromObject\n  FabricImage.fromObject = originUpdaterWrapper<FabricImage>(\n    FabricImage.fromObject,\n    originX,\n    originY,\n  );\n  Group.fromObject = originUpdaterWrapper<Group>(\n    Group.fromObject,\n    originX,\n    originY,\n  );\n};\n"], "names": ["originUpdaterWrapper", "originalFn", "defaultOriginX", "arguments", "length", "undefined", "defaultOriginY", "serializedObject", "originX", "originY", "_len", "args", "Array", "_key", "originalObject", "call", "actualPosition", "Point", "left", "top", "setPositionByOrigin", "installOriginWrapperUpdater", "BaseFabricObject", "_fromObject", "FabricImage", "fromObject", "Group"], "mappings": ";;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACaA,MAAAA,oBAAoB,GAAG,UAClCC,UAA0C,EAAA;AAAA,EAAA,IAC1CC,cAAwB,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,MAAM,CAAA;AAAA,EAAA,IACjCG,cAAwB,GAAAH,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;EAAA,OAEhC,gBAAyBI,gBAAgB,EAAW;AAClD;IACA,MAAM;AAAEC,MAAAA,OAAO,GAAGN,cAAc;AAAEO,MAAAA,OAAO,GAAGH,cAAAA;AAAe,KAAC,GAC1DC,gBAAgB,CAAA;AAClB;IACA,OAAOA,gBAAgB,CAACC,OAAO,CAAA;IAC/B,OAAOD,gBAAgB,CAACE,OAAO,CAAA;IAAC,KAAAC,IAAAA,IAAA,GAAAP,SAAA,CAAAC,MAAA,EANYO,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;AAAJF,MAAAA,IAAI,CAAAE,IAAA,GAAAV,CAAAA,CAAAA,GAAAA,SAAA,CAAAU,IAAA,CAAA,CAAA;AAAA,KAAA;AAOhD,IAAA,MAAMC,cAAc,GAAG,MAAMb,UAAU,CAACc,IAAI,CAC1C,IAAI,EACJR,gBAAgB,EAChB,GAAGI,IACL,CAAC,CAAA;AACD,IAAA,MAAMK,cAAc,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACI,IAAI,EAAEJ,cAAc,CAACK,GAAG,CAAC,CAAA;IACzEL,cAAc,CAACM,mBAAmB,CAACJ,cAAc,EAAER,OAAO,EAAEC,OAAO,CAAC,CAAA;AACpE,IAAA,OAAOK,cAAc,CAAA;GACtB,CAAA;AAAA,EAAA;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;MACaO,2BAA2B,GAAGA,CACzCb,OAAkB,EAClBC,OAAkB,KACf;AACH;AACAa,EAAAA,gBAAgB,CAACC,WAAW,GAAGvB,oBAAoB,CACjDsB,gBAAgB,CAACC,WAAW,EAC5Bf,OAAO,EACPC,OACF,CAAC,CAAA;AACD;AACAe,EAAAA,WAAW,CAACC,UAAU,GAAGzB,oBAAoB,CAC3CwB,WAAW,CAACC,UAAU,EACtBjB,OAAO,EACPC,OACF,CAAC,CAAA;AACDiB,EAAAA,KAAK,CAACD,UAAU,GAAGzB,oBAAoB,CACrC0B,KAAK,CAACD,UAAU,EAChBjB,OAAO,EACPC,OACF,CAAC,CAAA;AACH;;;;"}