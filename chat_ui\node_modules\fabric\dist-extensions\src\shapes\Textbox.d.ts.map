{"version": 3, "file": "Textbox.d.ts", "sourceRoot": "", "sources": ["../../../src/shapes/Textbox.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAC9D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAItC,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,KAAK,EAAE,oBAAoB,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AACtE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACjD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAKnD,eAAO,MAAM,oBAAoB,EAAE,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAOnE,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,SAAS,EAAE;QACT,IAAI,EAAE,MAAM,EAAE,CAAC;QACf,KAAK,EAAE,MAAM,CAAC;KACf,EAAE,EAAE,CAAC;IACN,gBAAgB,EAAE,MAAM,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,MAAM,EAAE,MAAM,CAAA;CAAE,CAAC,CAAC;AAGxE,UAAU,kBAAkB;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,OAAO,CAAC;IACzB,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,sBACf,SAAQ,oBAAoB,EAC1B,IAAI,CAAC,kBAAkB,EAAE,UAAU,GAAG,iBAAiB,CAAC;CAAG;AAE/D,MAAM,WAAW,YAAa,SAAQ,UAAU,EAAE,kBAAkB;CAAG;AAEvE;;;;;GAKG;AACH,qBAAa,OAAO,CAChB,KAAK,SAAS,QAAQ,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC,EAC5D,MAAM,SAAS,sBAAsB,GAAG,sBAAsB,EAC9D,SAAS,SAAS,WAAW,GAAG,WAAW,CAE7C,SAAQ,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CACtC,YAAW,kBAAkB;IAE7B;;;;OAIG;IACK,QAAQ,EAAE,MAAM,CAAC;IAEzB;;;;;;OAMG;IACK,eAAe,EAAE,MAAM,CAAC;IAEhC;;;;;OAKG;IACK,eAAe,EAAE,OAAO,CAAC;IAEzB,YAAY,EAAE,MAAM,CAAC;IAErB,SAAS,EAAE,QAAQ,CAAC;IAEpB,UAAU,EAAE,OAAO,CAAC;IAE5B,MAAM,CAAC,IAAI,SAAa;IAExB,MAAM,CAAC,oBAAoB,WAA4C;IAEvE,MAAM,CAAC,WAAW,iGAAwB;IAE1C,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAOzC;;;;OAIG;gBACS,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK;IAIzC;;;;OAIG;IACH,MAAM,CAAC,cAAc,IAAI;QAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAAE;IAI9D;;;;;OAKG;IACH,cAAc;IAsBd;;;;;;OAMG;IACH,iBAAiB,CAAC,QAAQ,EAAE,aAAa,GAAG,QAAQ;IA8BpD;;;;OAIG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,oBAAoB,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IAU1E;;;;OAIG;IACH,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAqCzC;;;;;OAKG;IACH,oBAAoB,CAClB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,GAChB,oBAAoB;IAYvB;;;;;OAKG;IACH,SAAS,CAAC,oBAAoB,CAC5B,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,MAAM;IAMf;;;;OAIG;IACH,SAAS,CAAC,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAKtE;;;;;;;OAOG;IACH,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAKnD;;;;;OAKG;IACH,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM;IAKzC;;;;;;;;OAQG;IACH,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE;IAY5D;;;;;;OAMG;IACH,wBAAwB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,YAAY;IAkCvD;;;;;;;;;;;OAWG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,SAAI,GAAG,MAAM;IAkBvE;;;;;OAKG;IACH,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE;IAIlC;;;;;;;;;;OAUG;IACH,SAAS,CACP,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,MAAM,EACpB,EAAE,gBAAgB,EAAE,SAAS,EAAE,EAAE,YAAY,EAC7C,aAAa,SAAI,GAChB,MAAM,EAAE,EAAE;IA6Db;;;;;OAKG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAY3C;;;;;;OAMG;IACH,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC;IAOtE;;;;;;OAMG;IACH,mBAAmB,CAAC,IAAI,EAAE,MAAM;IAYhC,WAAW;IAIX,uBAAuB;IAgBvB;;;;;OAKG;IACH,QAAQ,CACN,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAC5D,CAAC,SAAS,MAAM,CAAC,GAAG,KAAK,EACzB,mBAAmB,GAAE,CAAC,EAAO,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM;CAOtD"}