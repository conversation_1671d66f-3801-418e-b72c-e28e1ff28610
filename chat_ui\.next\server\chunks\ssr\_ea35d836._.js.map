{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/context.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useState, ReactNode } from 'react';\r\nimport * as fabric from 'fabric';\r\n\r\nexport type Tool = 'select' | 'rectangle' | 'circle' | 'text' | 'brush' | 'eraser' | 'image' | 'hand';\r\n\r\n// Define the shape of the context state\r\ninterface ImageEditorContextType {\r\n  canvas: fabric.Canvas | null;\r\n  setCanvas: (canvas: fabric.Canvas | null) => void;\r\n  activeTool: Tool;\r\n  setActiveTool: (tool: Tool) => void;\r\n  brushSize: number;\r\n  setBrushSize: (size: number) => void;\r\n  brushColor: string;\r\n  setBrushColor: (color: string) => void;\r\n  fillColor: string;\r\n  setFillColor: (color: string) => void;\r\n  strokeColor: string;\r\n  setStrokeColor: (color: string) => void;\r\n  strokeWidth: number;\r\n  setStrokeWidth: (width: number) => void;\r\n  isDrawing: boolean;\r\n  setIsDrawing: (drawing: boolean) => void;\r\n  // Canvas viewport state\r\n  zoom: number;\r\n  setZoom: (zoom: number) => void;\r\n  panX: number;\r\n  setPanX: (x: number) => void;\r\n  panY: number;\r\n  setPanY: (y: number) => void;\r\n  // Toolbar position\r\n  toolbarPosition: { x: number; y: number };\r\n  setToolbarPosition: (position: { x: number; y: number }) => void;\r\n}\r\n\r\n// Create the context with a default value\r\nconst ImageEditorContext = createContext<ImageEditorContextType | null>(null);\r\n\r\n// Create a provider component\r\ninterface ImageEditorProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ImageEditorProvider = ({ children }: ImageEditorProviderProps) => {\r\n  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);\r\n  const [activeTool, setActiveTool] = useState<Tool>('select');\r\n  const [brushSize, setBrushSize] = useState(5);\r\n  const [brushColor, setBrushColor] = useState('#000000');\r\n  const [fillColor, setFillColor] = useState('#ff0000');\r\n  const [strokeColor, setStrokeColor] = useState('#000000');\r\n  const [strokeWidth, setStrokeWidth] = useState(2);\r\n  const [isDrawing, setIsDrawing] = useState(false);\r\n\r\n  // Canvas viewport state\r\n  const [zoom, setZoom] = useState(1);\r\n  const [panX, setPanX] = useState(0);\r\n  const [panY, setPanY] = useState(0);\r\n\r\n  // Toolbar position\r\n  const [toolbarPosition, setToolbarPosition] = useState({ x: 500, y: 56 });\r\n\r\n  return (\r\n    <ImageEditorContext.Provider value={{\r\n      canvas,\r\n      setCanvas,\r\n      activeTool,\r\n      setActiveTool,\r\n      brushSize,\r\n      setBrushSize,\r\n      brushColor,\r\n      setBrushColor,\r\n      fillColor,\r\n      setFillColor,\r\n      strokeColor,\r\n      setStrokeColor,\r\n      strokeWidth,\r\n      setStrokeWidth,\r\n      isDrawing,\r\n      setIsDrawing,\r\n      zoom,\r\n      setZoom,\r\n      panX,\r\n      setPanX,\r\n      panY,\r\n      setPanY,\r\n      toolbarPosition,\r\n      setToolbarPosition\r\n    }}>\r\n      {children}\r\n    </ImageEditorContext.Provider>\r\n  );\r\n};\r\n\r\n// Create a custom hook to use the context\r\nexport const useImageEditor = () => {\r\n  const context = useContext(ImageEditorContext);\r\n  if (!context) {\r\n    throw new Error('useImageEditor must be used within an ImageEditorProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAqCA,0CAA0C;AAC1C,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAOjE,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA4B;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;IAAG;IAEvE,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAGO,MAAM,iBAAiB;IAC5B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,qMAAA,CAAA,UAAa,CAC3B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Canvas.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useRef, useCallback, useState } from 'react';\r\nimport * as fabric from 'fabric';\r\nimport { useImageEditor } from './context';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\n\r\nconst Canvas = () => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\r\n  const [isPanning, setIsPanning] = useState(false);\r\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\r\n\r\n  const {\r\n    canvas,\r\n    setCanvas,\r\n    activeTool,\r\n    brushSize,\r\n    brushColor,\r\n    fillColor,\r\n    strokeColor,\r\n    strokeWidth,\r\n    zoom,\r\n    setZoom,\r\n    panX,\r\n    setPanX,\r\n    panY,\r\n    setPanY\r\n  } = useImageEditor();\r\n\r\n  // Handle window resize to make canvas fullscreen\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setDimensions({\r\n        width: window.innerWidth,\r\n        height: window.innerHeight\r\n      });\r\n    };\r\n\r\n    handleResize();\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Handle wheel events for zooming\r\n  const handleWheel = useCallback((e: WheelEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (e.ctrlKey || e.metaKey) {\r\n      // Zoom with professional range (1% to 10,000%)\r\n      const delta = e.deltaY > 0 ? 0.9 : 1.1;\r\n      const newZoom = Math.max(0.01, Math.min(100, zoom * delta));\r\n      setZoom(newZoom);\r\n\r\n      if (canvas) {\r\n        canvas.setZoom(newZoom);\r\n        canvas.renderAll();\r\n      }\r\n    } else {\r\n      // Pan\r\n      setPanX(panX - e.deltaX);\r\n      setPanY(panY - e.deltaY);\r\n\r\n      if (canvas) {\r\n        canvas.relativePan(new fabric.Point(-e.deltaX, -e.deltaY));\r\n      }\r\n    }\r\n  }, [canvas, zoom, setZoom, panX, setPanX, panY, setPanY]);\r\n\r\n  // Handle panning with space + drag or hand tool\r\n  const handleMouseDown = useCallback((e: MouseEvent) => {\r\n    if (activeTool === 'hand' || e.button === 1) {\r\n      setIsPanning(true);\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n      e.preventDefault();\r\n    }\r\n  }, [activeTool]);\r\n\r\n  const handleMouseMove = useCallback((e: MouseEvent) => {\r\n    if (isPanning) {\r\n      const deltaX = e.clientX - lastPanPoint.x;\r\n      const deltaY = e.clientY - lastPanPoint.y;\r\n\r\n      setPanX(panX + deltaX);\r\n      setPanY(panY + deltaY);\r\n\r\n      if (canvas) {\r\n        canvas.relativePan(new fabric.Point(deltaX, deltaY));\r\n      }\r\n\r\n      setLastPanPoint({ x: e.clientX, y: e.clientY });\r\n    }\r\n  }, [isPanning, lastPanPoint, canvas, panX, setPanX, panY, setPanY]);\r\n\r\n  const handleMouseUp = useCallback(() => {\r\n    setIsPanning(false);\r\n  }, []);\r\n\r\n  // Handle keyboard shortcuts\r\n  useEffect(() => {\r\n    const handleKeyDown = (e: KeyboardEvent) => {\r\n      // Space key for temporary hand tool\r\n      if (e.code === 'Space' && !e.repeat) {\r\n        e.preventDefault();\r\n        document.body.style.cursor = 'grab';\r\n      }\r\n\r\n      // Zoom shortcuts\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '0') {\r\n        e.preventDefault();\r\n        setZoom(1);\r\n        if (canvas) {\r\n          canvas.setZoom(1);\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom In (Ctrl/Cmd + Plus/Equal)\r\n      if ((e.ctrlKey || e.metaKey) && (e.key === '=' || e.key === '+')) {\r\n        e.preventDefault();\r\n        const newZoom = Math.min(100, zoom * 1.25);\r\n        setZoom(newZoom);\r\n        if (canvas) {\r\n          canvas.setZoom(newZoom);\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Zoom Out (Ctrl/Cmd + Minus)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '-') {\r\n        e.preventDefault();\r\n        const newZoom = Math.max(0.01, zoom * 0.8);\r\n        setZoom(newZoom);\r\n        if (canvas) {\r\n          canvas.setZoom(newZoom);\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n\r\n      // Fit to Screen (Ctrl/Cmd + 1)\r\n      if ((e.ctrlKey || e.metaKey) && e.key === '1') {\r\n        e.preventDefault();\r\n        setZoom(1);\r\n        if (canvas) {\r\n          canvas.setZoom(1);\r\n          canvas.renderAll();\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleKeyUp = (e: KeyboardEvent) => {\r\n      if (e.code === 'Space') {\r\n        document.body.style.cursor = 'default';\r\n      }\r\n    };\r\n\r\n    window.addEventListener('keydown', handleKeyDown);\r\n    window.addEventListener('keyup', handleKeyUp);\r\n\r\n    return () => {\r\n      window.removeEventListener('keydown', handleKeyDown);\r\n      window.removeEventListener('keyup', handleKeyUp);\r\n    };\r\n  }, [canvas, setZoom]);\r\n\r\n  // Update drawing mode when tool changes\r\n  useEffect(() => {\r\n    if (!canvas) return;\r\n\r\n    // Store the active tool and properties on the canvas for event handlers\r\n    (canvas as any).activeTool = activeTool;\r\n    (canvas as any).fillColor = fillColor;\r\n    (canvas as any).strokeColor = strokeColor;\r\n    (canvas as any).strokeWidth = strokeWidth;\r\n\r\n    if (activeTool === 'brush') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        canvas.freeDrawingBrush.color = brushColor;\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    } else if (activeTool === 'eraser') {\r\n      canvas.isDrawingMode = true;\r\n      if (canvas.freeDrawingBrush) {\r\n        canvas.freeDrawingBrush.width = brushSize;\r\n        // Set eraser mode by using destination-out composite operation\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'destination-out';\r\n      }\r\n    } else {\r\n      canvas.isDrawingMode = false;\r\n      // Reset composite operation\r\n      if (canvas.freeDrawingBrush) {\r\n        (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';\r\n      }\r\n    }\r\n  }, [canvas, activeTool, brushSize, brushColor, fillColor, strokeColor, strokeWidth]);\r\n\r\n  const setupCanvasEvents = useCallback((canvas: fabric.Canvas) => {\r\n    // Handle mouse events for shape creation\r\n    let isDown = false;\r\n    let origX = 0;\r\n    let origY = 0;\r\n    let shape: fabric.Object | null = null;\r\n\r\n    const onMouseDown = (o: any) => {\r\n      // Get current tool from the canvas data or use a closure\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n      if (currentTool === 'select' || currentTool === 'brush' || currentTool === 'eraser' || currentTool === 'hand') return;\r\n\r\n      isDown = true;\r\n      const pointer = canvas.getPointer(o.e);\r\n      origX = pointer.x;\r\n      origY = pointer.y;\r\n\r\n      if (currentTool === 'rectangle') {\r\n        shape = new fabric.Rect({\r\n          left: origX,\r\n          top: origY,\r\n          width: 0,\r\n          height: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      } else if (currentTool === 'circle') {\r\n        shape = new fabric.Circle({\r\n          left: origX,\r\n          top: origY,\r\n          radius: 0,\r\n          fill: (canvas as any).fillColor || fillColor,\r\n          stroke: (canvas as any).strokeColor || strokeColor,\r\n          strokeWidth: (canvas as any).strokeWidth || strokeWidth,\r\n          selectable: false,\r\n        });\r\n      }\r\n\r\n      if (shape) {\r\n        canvas.add(shape);\r\n      }\r\n    };\r\n\r\n    const onMouseMove = (o: any) => {\r\n      if (!isDown || !shape) return;\r\n\r\n      const pointer = canvas.getPointer(o.e);\r\n      const currentTool = (canvas as any).activeTool || activeTool;\r\n\r\n      if (currentTool === 'rectangle') {\r\n        const rect = shape as fabric.Rect;\r\n        rect.set({\r\n          width: Math.abs(pointer.x - origX),\r\n          height: Math.abs(pointer.y - origY),\r\n        });\r\n        if (pointer.x < origX) {\r\n          rect.set({ left: pointer.x });\r\n        }\r\n        if (pointer.y < origY) {\r\n          rect.set({ top: pointer.y });\r\n        }\r\n      } else if (currentTool === 'circle') {\r\n        const circle = shape as fabric.Circle;\r\n        const radius = Math.sqrt(Math.pow(pointer.x - origX, 2) + Math.pow(pointer.y - origY, 2)) / 2;\r\n        circle.set({ radius });\r\n      }\r\n\r\n      canvas.renderAll();\r\n    };\r\n\r\n    const onMouseUp = () => {\r\n      if (shape) {\r\n        shape.set({ selectable: true });\r\n        shape = null;\r\n      }\r\n      isDown = false;\r\n    };\r\n\r\n    canvas.on('mouse:down', onMouseDown);\r\n    canvas.on('mouse:move', onMouseMove);\r\n    canvas.on('mouse:up', onMouseUp);\r\n\r\n    return () => {\r\n      canvas.off('mouse:down', onMouseDown);\r\n      canvas.off('mouse:move', onMouseMove);\r\n      canvas.off('mouse:up', onMouseUp);\r\n    };\r\n  }, [activeTool, fillColor, strokeColor, strokeWidth]);\r\n\r\n  // Initialize canvas when dimensions are available\r\n  useEffect(() => {\r\n    if (canvasRef.current && dimensions.width > 0 && dimensions.height > 0) {\r\n      const fabricCanvas = new fabric.Canvas(canvasRef.current, {\r\n        width: dimensions.width,\r\n        height: dimensions.height,\r\n        backgroundColor: 'transparent',\r\n      });\r\n\r\n      // Enable viewport transform for panning and zooming\r\n      fabricCanvas.selection = true;\r\n\r\n      setCanvas(fabricCanvas);\r\n\r\n      // Clean up on unmount\r\n      return () => {\r\n        fabricCanvas.dispose();\r\n      };\r\n    }\r\n  }, [dimensions, setCanvas]);\r\n\r\n  // Setup events once when canvas is created\r\n  useEffect(() => {\r\n    if (canvas) {\r\n      const cleanup = setupCanvasEvents(canvas);\r\n      return cleanup;\r\n    }\r\n  }, [canvas, setupCanvasEvents]);\r\n\r\n  // Add event listeners for mouse and wheel events\r\n  useEffect(() => {\r\n    const container = containerRef.current;\r\n    if (!container) return;\r\n\r\n    container.addEventListener('wheel', handleWheel, { passive: false });\r\n    container.addEventListener('mousedown', handleMouseDown);\r\n    container.addEventListener('mousemove', handleMouseMove);\r\n    container.addEventListener('mouseup', handleMouseUp);\r\n\r\n    return () => {\r\n      container.removeEventListener('wheel', handleWheel);\r\n      container.removeEventListener('mousedown', handleMouseDown);\r\n      container.removeEventListener('mousemove', handleMouseMove);\r\n      container.removeEventListener('mouseup', handleMouseUp);\r\n    };\r\n  }, [handleWheel, handleMouseDown, handleMouseMove, handleMouseUp]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className=\"w-[calc(100vw-271px)]  overflow-hidden\"\r\n      style={{\r\n        background: `\r\n          radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\r\n          radial-gradient(circle at 75% 75%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\r\n          linear-gradient(135deg, #667eea 0%, #764ba2 100%)\r\n        `,\r\n        cursor: activeTool === 'hand' ? 'grab' : isPanning ? 'grabbing' : 'default'\r\n      }}\r\n    >\r\n      {/* Infinite grid background */}\r\n      <div\r\n        className=\"absolute inset-0\"\r\n        style={{\r\n          backgroundImage: `\r\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\r\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\r\n          `,\r\n          backgroundSize: `${20 * zoom}px ${20 * zoom}px`,\r\n          backgroundPosition: `${panX}px ${panY}px`,\r\n          transform: `scale(${zoom})`,\r\n          transformOrigin: '0 0'\r\n        }}\r\n      />\r\n\r\n      {/* Canvas */}\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"absolute top-0 left-0 z-10\"\r\n        style={{\r\n          display: 'block',\r\n          width: dimensions.width,\r\n          height: dimensions.height\r\n        }}\r\n      />\r\n\r\n      {/* Zoom indicator with interactive slider */}\r\n      <TooltipProvider>\r\n        <div className=\"fixed bottom-4 right-4\">\r\n          <Popover>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <PopoverTrigger asChild>\r\n                  <button className=\"bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-lg text-sm hover:bg-black/60 transition-colors cursor-pointer\">\r\n                    {Math.round(zoom * 100)}%\r\n                  </button>\r\n                </PopoverTrigger>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>Click to adjust zoom level</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n            <PopoverContent className=\"w-80 bg-black/80 backdrop-blur-md border-white/20\" side=\"top\">\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <label className=\"text-sm font-medium text-white\">Zoom Level</label>\r\n                  <span className=\"text-sm text-white/70\">{Math.round(zoom * 100)}%</span>\r\n                </div>\r\n                <Slider\r\n                  value={[zoom * 100]}\r\n                  onValueChange={(value) => {\r\n                    const newZoom = value[0] / 100;\r\n                    setZoom(newZoom);\r\n                    if (canvas) {\r\n                      canvas.setZoom(newZoom);\r\n                      canvas.renderAll();\r\n                    }\r\n                  }}\r\n                  max={10000}\r\n                  min={1}\r\n                  step={1}\r\n                  className=\"flex-1\"\r\n                />\r\n                {/* Zoom Presets */}\r\n                <div className=\"space-y-3\">\r\n                  <div className=\"flex gap-1 flex-wrap justify-center\">\r\n                    {[25, 50, 75, 100, 125, 150, 200, 400, 800, 1600].map((percent) => (\r\n                      <button\r\n                        key={percent}\r\n                        onClick={() => {\r\n                          const newZoom = percent / 100;\r\n                          setZoom(newZoom);\r\n                          if (canvas) {\r\n                            canvas.setZoom(newZoom);\r\n                            canvas.renderAll();\r\n                          }\r\n                        }}\r\n                        className={`px-2 py-1 text-xs rounded transition-colors ${\r\n                          Math.round(zoom * 100) === percent\r\n                            ? 'bg-white/30 text-white font-medium'\r\n                            : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-white'\r\n                        }`}\r\n                      >\r\n                        {percent}%\r\n                      </button>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {/* Range Labels */}\r\n                  <div className=\"flex items-center justify-between text-xs text-white/50\">\r\n                    <span>1%</span>\r\n                    <span className=\"text-white/70\">Professional Zoom Range</span>\r\n                    <span>10000%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n      </TooltipProvider>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Canvas;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;AAUA,MAAM,SAAS;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE9D,MAAM,EACJ,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACR,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEjB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,EAAE,cAAc;QAEhB,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;YAC1B,+CAA+C;YAC/C,MAAM,QAAQ,EAAE,MAAM,GAAG,IAAI,MAAM;YACnC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO;YACpD,QAAQ;YAER,IAAI,QAAQ;gBACV,OAAO,OAAO,CAAC;gBACf,OAAO,SAAS;YAClB;QACF,OAAO;YACL,MAAM;YACN,QAAQ,OAAO,EAAE,MAAM;YACvB,QAAQ,OAAO,EAAE,MAAM;YAEvB,IAAI,QAAQ;gBACV,OAAO,WAAW,CAAC,IAAI,+IAAA,CAAA,QAAY,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM;YAC1D;QACF;IACF,GAAG;QAAC;QAAQ;QAAM;QAAS;QAAM;QAAS;QAAM;KAAQ;IAExD,gDAAgD;IAChD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,eAAe,UAAU,EAAE,MAAM,KAAK,GAAG;YAC3C,aAAa;YACb,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC7C,EAAE,cAAc;QAClB;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,WAAW;YACb,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;YACzC,MAAM,SAAS,EAAE,OAAO,GAAG,aAAa,CAAC;YAEzC,QAAQ,OAAO;YACf,QAAQ,OAAO;YAEf,IAAI,QAAQ;gBACV,OAAO,WAAW,CAAC,IAAI,+IAAA,CAAA,QAAY,CAAC,QAAQ;YAC9C;YAEA,gBAAgB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAC/C;IACF,GAAG;QAAC;QAAW;QAAc;QAAQ;QAAM;QAAS;QAAM;KAAQ;IAElE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,aAAa;IACf,GAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,oCAAoC;YACpC,IAAI,EAAE,IAAI,KAAK,WAAW,CAAC,EAAE,MAAM,EAAE;gBACnC,EAAE,cAAc;gBAChB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B;YAEA,iBAAiB;YACjB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,QAAQ;gBACR,IAAI,QAAQ;oBACV,OAAO,OAAO,CAAC;oBACf,OAAO,SAAS;gBAClB;YACF;YAEA,kCAAkC;YAClC,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,CAAC,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,KAAK,GAAG,GAAG;gBAChE,EAAE,cAAc;gBAChB,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,OAAO;gBACrC,QAAQ;gBACR,IAAI,QAAQ;oBACV,OAAO,OAAO,CAAC;oBACf,OAAO,SAAS;gBAClB;YACF;YAEA,8BAA8B;YAC9B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,OAAO;gBACtC,QAAQ;gBACR,IAAI,QAAQ;oBACV,OAAO,OAAO,CAAC;oBACf,OAAO,SAAS;gBAClB;YACF;YAEA,+BAA+B;YAC/B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,QAAQ;gBACR,IAAI,QAAQ;oBACV,OAAO,OAAO,CAAC;oBACf,OAAO,SAAS;gBAClB;YACF;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,EAAE,IAAI,KAAK,SAAS;gBACtB,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,SAAS;QAEjC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,SAAS;QACtC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;QAEb,wEAAwE;QACvE,OAAe,UAAU,GAAG;QAC5B,OAAe,SAAS,GAAG;QAC3B,OAAe,WAAW,GAAG;QAC7B,OAAe,WAAW,GAAG;QAE9B,IAAI,eAAe,SAAS;YAC1B,OAAO,aAAa,GAAG;YACvB,IAAI,OAAO,gBAAgB,EAAE;gBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAChC,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAC/B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF,OAAO,IAAI,eAAe,UAAU;YAClC,OAAO,aAAa,GAAG;YACvB,IAAI,OAAO,gBAAgB,EAAE;gBAC3B,OAAO,gBAAgB,CAAC,KAAK,GAAG;gBAChC,+DAA+D;gBAC9D,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF,OAAO;YACL,OAAO,aAAa,GAAG;YACvB,4BAA4B;YAC5B,IAAI,OAAO,gBAAgB,EAAE;gBAC1B,OAAO,gBAAgB,CAAS,wBAAwB,GAAG;YAC9D;QACF;IACF,GAAG;QAAC;QAAQ;QAAY;QAAW;QAAY;QAAW;QAAa;KAAY;IAEnF,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,yCAAyC;QACzC,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAI,QAAQ;QACZ,IAAI,QAA8B;QAElC,MAAM,cAAc,CAAC;YACnB,yDAAyD;YACzD,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;YAClD,IAAI,gBAAgB,YAAY,gBAAgB,WAAW,gBAAgB,YAAY,gBAAgB,QAAQ;YAE/G,SAAS;YACT,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YACrC,QAAQ,QAAQ,CAAC;YACjB,QAAQ,QAAQ,CAAC;YAEjB,IAAI,gBAAgB,aAAa;gBAC/B,QAAQ,IAAI,+IAAA,CAAA,OAAW,CAAC;oBACtB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,MAAM,AAAC,OAAe,SAAS,IAAI;oBACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;oBACvC,aAAa,AAAC,OAAe,WAAW,IAAI;oBAC5C,YAAY;gBACd;YACF,OAAO,IAAI,gBAAgB,UAAU;gBACnC,QAAQ,IAAI,+IAAA,CAAA,SAAa,CAAC;oBACxB,MAAM;oBACN,KAAK;oBACL,QAAQ;oBACR,MAAM,AAAC,OAAe,SAAS,IAAI;oBACnC,QAAQ,AAAC,OAAe,WAAW,IAAI;oBACvC,aAAa,AAAC,OAAe,WAAW,IAAI;oBAC5C,YAAY;gBACd;YACF;YAEA,IAAI,OAAO;gBACT,OAAO,GAAG,CAAC;YACb;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO;YAEvB,MAAM,UAAU,OAAO,UAAU,CAAC,EAAE,CAAC;YACrC,MAAM,cAAc,AAAC,OAAe,UAAU,IAAI;YAElD,IAAI,gBAAgB,aAAa;gBAC/B,MAAM,OAAO;gBACb,KAAK,GAAG,CAAC;oBACP,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;oBAC5B,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG;gBAC/B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,MAAM,QAAQ,CAAC;oBAAC;gBAC7B;gBACA,IAAI,QAAQ,CAAC,GAAG,OAAO;oBACrB,KAAK,GAAG,CAAC;wBAAE,KAAK,QAAQ,CAAC;oBAAC;gBAC5B;YACF,OAAO,IAAI,gBAAgB,UAAU;gBACnC,MAAM,SAAS;gBACf,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,OAAO,MAAM;gBAC5F,OAAO,GAAG,CAAC;oBAAE;gBAAO;YACtB;YAEA,OAAO,SAAS;QAClB;QAEA,MAAM,YAAY;YAChB,IAAI,OAAO;gBACT,MAAM,GAAG,CAAC;oBAAE,YAAY;gBAAK;gBAC7B,QAAQ;YACV;YACA,SAAS;QACX;QAEA,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,cAAc;QACxB,OAAO,EAAE,CAAC,YAAY;QAEtB,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,cAAc;YACzB,OAAO,GAAG,CAAC,YAAY;QACzB;IACF,GAAG;QAAC;QAAY;QAAW;QAAa;KAAY;IAEpD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,IAAI,WAAW,KAAK,GAAG,KAAK,WAAW,MAAM,GAAG,GAAG;YACtE,MAAM,eAAe,IAAI,+IAAA,CAAA,SAAa,CAAC,UAAU,OAAO,EAAE;gBACxD,OAAO,WAAW,KAAK;gBACvB,QAAQ,WAAW,MAAM;gBACzB,iBAAiB;YACnB;YAEA,oDAAoD;YACpD,aAAa,SAAS,GAAG;YAEzB,UAAU;YAEV,sBAAsB;YACtB,OAAO;gBACL,aAAa,OAAO;YACtB;QACF;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,MAAM,UAAU,kBAAkB;YAClC,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;KAAkB;IAE9B,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,UAAU,gBAAgB,CAAC,SAAS,aAAa;YAAE,SAAS;QAAM;QAClE,UAAU,gBAAgB,CAAC,aAAa;QACxC,UAAU,gBAAgB,CAAC,aAAa;QACxC,UAAU,gBAAgB,CAAC,WAAW;QAEtC,OAAO;YACL,UAAU,mBAAmB,CAAC,SAAS;YACvC,UAAU,mBAAmB,CAAC,aAAa;YAC3C,UAAU,mBAAmB,CAAC,aAAa;YAC3C,UAAU,mBAAmB,CAAC,WAAW;QAC3C;IACF,GAAG;QAAC;QAAa;QAAiB;QAAiB;KAAc;IAEjE,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,YAAY,CAAC;;;;QAIb,CAAC;YACD,QAAQ,eAAe,SAAS,SAAS,YAAY,aAAa;QACpE;;0BAGA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;UAGlB,CAAC;oBACD,gBAAgB,GAAG,KAAK,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,CAAC;oBAC/C,oBAAoB,GAAG,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC;oBACzC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAC3B,iBAAiB;gBACnB;;;;;;0BAIF,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,SAAS;oBACT,OAAO,WAAW,KAAK;oBACvB,QAAQ,WAAW,MAAM;gBAC3B;;;;;;0BAIF,8OAAC,4HAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0CACN,8OAAC,4HAAA,CAAA,UAAO;;kDACN,8OAAC,4HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC;gDAAO,WAAU;;oDACf,KAAK,KAAK,CAAC,OAAO;oDAAK;;;;;;;;;;;;;;;;;kDAI9B,8OAAC,4HAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC;sDAAE;;;;;;;;;;;;;;;;;0CAGP,8OAAC,4HAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAoD,MAAK;0CACjF,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,8OAAC;oDAAK,WAAU;;wDAAyB,KAAK,KAAK,CAAC,OAAO;wDAAK;;;;;;;;;;;;;sDAElE,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;gDAAC,OAAO;6CAAI;4CACnB,eAAe,CAAC;gDACd,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG;gDAC3B,QAAQ;gDACR,IAAI,QAAQ;oDACV,OAAO,OAAO,CAAC;oDACf,OAAO,SAAS;gDAClB;4CACF;4CACA,KAAK;4CACL,KAAK;4CACL,MAAM;4CACN,WAAU;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAI;wDAAI;wDAAI;wDAAK;wDAAK;wDAAK;wDAAK;wDAAK;wDAAK;qDAAK,CAAC,GAAG,CAAC,CAAC,wBACrD,8OAAC;4DAEC,SAAS;gEACP,MAAM,UAAU,UAAU;gEAC1B,QAAQ;gEACR,IAAI,QAAQ;oEACV,OAAO,OAAO,CAAC;oEACf,OAAO,SAAS;gEAClB;4DACF;4DACA,WAAW,CAAC,4CAA4C,EACtD,KAAK,KAAK,CAAC,OAAO,SAAS,UACvB,uCACA,gEACJ;;gEAED;gEAAQ;;2DAfJ;;;;;;;;;;8DAqBX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;uCAEe", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-9 px-2 min-w-9\",\n        sm: \"h-8 px-1.5 min-w-8\",\n        lg: \"h-10 px-2.5 min-w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Toggle({\n  className,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof TogglePrimitive.Root> &\n  VariantProps<typeof toggleVariants>) {\n  return (\n    <TogglePrimitive.Root\n      data-slot=\"toggle\"\n      className={cn(toggleVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Toggle, toggleVariants }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ijBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,GAAG,OAEgC;IACnC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/Toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useImageEditor, Tool } from './context';\r\nimport * as fabric from 'fabric';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Toggle } from '@/components/ui/toggle';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport {\r\n  MousePointer2,\r\n  Square,\r\n  Circle,\r\n  Type,\r\n  Brush,\r\n  Eraser,\r\n  Image,\r\n  Download,\r\n  Upload,\r\n  Trash2,\r\n  Copy,\r\n  Layers,\r\n  Hand,\r\n  Move\r\n} from 'lucide-react';\r\nimport React, { useRef, useState } from 'react';\r\n\r\nconst Toolbar = () => {\r\n  const {\r\n    canvas,\r\n    activeTool,\r\n    setActiveTool,\r\n    brushSize,\r\n    setBrushSize,\r\n    brushColor,\r\n    setBrushColor,\r\n    fillColor,\r\n    setFillColor,\r\n    strokeColor,\r\n    setStrokeColor,\r\n    strokeWidth,\r\n    setStrokeWidth,\r\n    toolbarPosition,\r\n    setToolbarPosition\r\n  } = useImageEditor();\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });\r\n\r\n  const tools: { id: Tool; icon: React.ReactNode; label: string }[] = [\r\n    { id: 'select', icon: <MousePointer2 size={18} />, label: 'Select' },\r\n    { id: 'hand', icon: <Hand size={18} />, label: 'Hand' },\r\n    { id: 'rectangle', icon: <Square size={18} />, label: 'Rectangle' },\r\n    { id: 'circle', icon: <Circle size={18} />, label: 'Circle' },\r\n    { id: 'text', icon: <Type size={18} />, label: 'Text' },\r\n    { id: 'brush', icon: <Brush size={18} />, label: 'Brush' },\r\n    { id: 'eraser', icon: <Eraser size={18} />, label: 'Eraser' },\r\n    { id: 'image', icon: <Image size={18} />, label: 'Image' },\r\n  ];\r\n\r\n  const handleToolSelect = (tool: Tool) => {\r\n    setActiveTool(tool);\r\n\r\n    if (tool === 'text' && canvas) {\r\n      const text = new fabric.IText('Click to edit', {\r\n        left: 100,\r\n        top: 100,\r\n        fontFamily: 'Arial',\r\n        fontSize: 20,\r\n        fill: fillColor,\r\n      });\r\n      canvas.add(text);\r\n      canvas.setActiveObject(text);\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file && canvas) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        const imgUrl = e.target?.result as string;\r\n        fabric.FabricImage.fromURL(imgUrl).then((img: any) => {\r\n          img.set({\r\n            left: 50,\r\n            top: 50,\r\n            scaleX: 0.5,\r\n            scaleY: 0.5,\r\n          });\r\n          canvas.add(img);\r\n          canvas.renderAll();\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    if (canvas) {\r\n      const dataURL = canvas.toDataURL({\r\n        format: 'png',\r\n        quality: 1,\r\n        multiplier: 1,\r\n      });\r\n      const link = document.createElement('a');\r\n      link.download = 'design.png';\r\n      link.href = dataURL;\r\n      link.click();\r\n    }\r\n  };\r\n\r\n  const handleClear = () => {\r\n    if (canvas) {\r\n      canvas.clear();\r\n      canvas.renderAll();\r\n    }\r\n  };\r\n\r\n  const handleCopy = () => {\r\n    if (canvas) {\r\n      const activeObject = canvas.getActiveObject();\r\n      if (activeObject) {\r\n        // Simple duplication by creating a new object with same properties\r\n        const objData = activeObject.toObject();\r\n        const cloned = new (activeObject.constructor as any)(objData);\r\n        cloned.set({\r\n          left: (activeObject.left || 0) + 10,\r\n          top: (activeObject.top || 0) + 10,\r\n        });\r\n        canvas.add(cloned);\r\n        canvas.setActiveObject(cloned);\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  const deleteSelected = () => {\r\n    if (canvas) {\r\n      const activeObjects = canvas.getActiveObjects();\r\n      if (activeObjects.length) {\r\n        activeObjects.forEach((obj) => canvas.remove(obj));\r\n        canvas.discardActiveObject();\r\n        canvas.renderAll();\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle toolbar dragging\r\n  const handleMouseDown = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains('drag-handle')) {\r\n      setIsDragging(true);\r\n      setDragOffset({\r\n        x: e.clientX - toolbarPosition.x,\r\n        y: e.clientY - toolbarPosition.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseMove = (e: MouseEvent) => {\r\n    if (isDragging) {\r\n      setToolbarPosition({\r\n        x: e.clientX - dragOffset.x,\r\n        y: e.clientY - dragOffset.y\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleMouseUp = () => {\r\n    setIsDragging(false);\r\n  };\r\n\r\n  // Add global mouse event listeners for dragging\r\n  React.useEffect(() => {\r\n    if (isDragging) {\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n      document.addEventListener('mouseup', handleMouseUp);\r\n      return () => {\r\n        document.removeEventListener('mousemove', handleMouseMove);\r\n        document.removeEventListener('mouseup', handleMouseUp);\r\n      };\r\n    }\r\n  }, [isDragging, dragOffset]);\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div\r\n        className=\"fixed z-50 flex flex-wrap items-center gap-2 p-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg cursor-move select-none\"\r\n        style={{\r\n          left: toolbarPosition.x,\r\n          top: toolbarPosition.y,\r\n          maxWidth: '90vw'\r\n        }}\r\n        onMouseDown={handleMouseDown}\r\n      >\r\n        {/* Drag Handle */}\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <div className=\"drag-handle flex items-center justify-center w-6 h-6 text-white/50 hover:text-white/80 cursor-grab active:cursor-grabbing\">\r\n              <Move size={14} />\r\n            </div>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Drag to move toolbar</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n        {/* Tool Selection */}\r\n        <div className=\"flex items-center gap-1 bg-white/5 rounded-lg p-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n          {tools.map((tool) => (\r\n            <Tooltip key={tool.id}>\r\n              <TooltipTrigger asChild>\r\n                <Toggle\r\n                  pressed={activeTool === tool.id}\r\n                  onPressedChange={() => handleToolSelect(tool.id)}\r\n                  className=\"h-10 w-10 data-[state=on]:bg-white/20 data-[state=on]:text-white hover:bg-white/10\"\r\n                  aria-label={tool.label}\r\n                >\r\n                  {tool.icon}\r\n                </Toggle>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{tool.label}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          ))}\r\n        </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Color Controls */}\r\n      <div className=\"flex items-center gap-2\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border border-white/30\"\r\n                    style={{ backgroundColor: fillColor }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Fill Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Fill Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={fillColor}\r\n                onChange={(e) => setFillColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n\r\n        <Popover>\r\n          <Tooltip>\r\n            <TooltipTrigger asChild>\r\n              <PopoverTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                >\r\n                  <div\r\n                    className=\"w-full h-full rounded border-2\"\r\n                    style={{ borderColor: strokeColor, backgroundColor: 'transparent' }}\r\n                  />\r\n                </Button>\r\n              </PopoverTrigger>\r\n            </TooltipTrigger>\r\n            <TooltipContent>\r\n              <p>Stroke Color</p>\r\n            </TooltipContent>\r\n          </Tooltip>\r\n          <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n            <div className=\"space-y-3\">\r\n              <label className=\"text-sm font-medium text-white\">Stroke Color</label>\r\n              <input\r\n                type=\"color\"\r\n                value={strokeColor}\r\n                onChange={(e) => setStrokeColor(e.target.value)}\r\n                className=\"w-full h-10 rounded border-0\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Size Controls */}\r\n      {(activeTool === 'brush' || activeTool === 'eraser') && (\r\n        <>\r\n          <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n            <Brush size={16} className=\"text-white/70\" />\r\n            <Slider\r\n              value={[brushSize]}\r\n              onValueChange={(value) => setBrushSize(value[0])}\r\n              max={50}\r\n              min={1}\r\n              step={1}\r\n              className=\"flex-1\"\r\n            />\r\n            <span className=\"text-xs text-white/70 w-6\">{brushSize}</span>\r\n          </div>\r\n\r\n          {activeTool === 'brush' && (\r\n            <Popover>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <PopoverTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"h-10 w-16 p-1 bg-white/5 border-white/20 hover:bg-white/10\"\r\n                    >\r\n                      <div\r\n                        className=\"w-full h-full rounded border border-white/30\"\r\n                        style={{ backgroundColor: brushColor }}\r\n                      />\r\n                    </Button>\r\n                  </PopoverTrigger>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Brush Color</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n              <PopoverContent className=\"w-64 bg-black/80 backdrop-blur-md border-white/20\">\r\n                <div className=\"space-y-3\">\r\n                  <label className=\"text-sm font-medium text-white\">Brush Color</label>\r\n                  <input\r\n                    type=\"color\"\r\n                    value={brushColor}\r\n                    onChange={(e) => setBrushColor(e.target.value)}\r\n                    className=\"w-full h-10 rounded border-0\"\r\n                  />\r\n                </div>\r\n              </PopoverContent>\r\n            </Popover>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {(activeTool === 'rectangle' || activeTool === 'circle') && (\r\n        <div className=\"flex items-center gap-2 min-w-[120px]\" onMouseDown={(e) => e.stopPropagation()}>\r\n          <div className=\"w-4 h-0.5 bg-white/70 rounded\" />\r\n          <Slider\r\n            value={[strokeWidth]}\r\n            onValueChange={(value) => setStrokeWidth(value[0])}\r\n            max={20}\r\n            min={0}\r\n            step={1}\r\n            className=\"flex-1\"\r\n          />\r\n          <span className=\"text-xs text-white/70 w-6\">{strokeWidth}</span>\r\n        </div>\r\n      )}\r\n\r\n      <Separator orientation=\"vertical\" className=\"h-8 bg-white/20\" />\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center gap-1\" onMouseDown={(e) => e.stopPropagation()}>\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => fileInputRef.current?.click()}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Upload size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Upload Image</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleDownload}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Download size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Download Design</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopy}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Copy size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Duplicate Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={deleteSelected}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-white/70 hover:text-white\"\r\n            >\r\n              <Trash2 size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Delete Selected</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleClear}\r\n              className=\"h-10 w-10 p-0 hover:bg-white/10 text-red-400 hover:text-red-300\"\r\n            >\r\n              <Layers size={18} />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>\r\n            <p>Clear Canvas</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </div>\r\n    </div>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default Toolbar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AA1BA;;;;;;;;;;;;AA4BA,MAAM,UAAU;IACd,MAAM,EACJ,MAAM,EACN,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,WAAW,EACX,cAAc,EACd,eAAe,EACf,kBAAkB,EACnB,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE1D,MAAM,QAA8D;QAClE;YAAE,IAAI;YAAU,oBAAM,8OAAC,4NAAA,CAAA,gBAAa;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QACnE;YAAE,IAAI;YAAQ,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAa,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAY;QAClE;YAAE,IAAI;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QAC5D;YAAE,IAAI;YAAQ,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAO;QACtD;YAAE,IAAI;YAAS,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAQ;QACzD;YAAE,IAAI;YAAU,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAS;QAC5D;YAAE,IAAI;YAAS,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YAAQ,OAAO;QAAQ;KAC1D;IAED,MAAM,mBAAmB,CAAC;QACxB,cAAc;QAEd,IAAI,SAAS,UAAU,QAAQ;YAC7B,MAAM,OAAO,IAAI,+IAAA,CAAA,QAAY,CAAC,iBAAiB;gBAC7C,MAAM;gBACN,KAAK;gBACL,YAAY;gBACZ,UAAU;gBACV,MAAM;YACR;YACA,OAAO,GAAG,CAAC;YACX,OAAO,eAAe,CAAC;YACvB,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,QAAQ,QAAQ;YAClB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,SAAS,EAAE,MAAM,EAAE;gBACzB,+IAAA,CAAA,cAAkB,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACvC,IAAI,GAAG,CAAC;wBACN,MAAM;wBACN,KAAK;wBACL,QAAQ;wBACR,QAAQ;oBACV;oBACA,OAAO,GAAG,CAAC;oBACX,OAAO,SAAS;gBAClB;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,UAAU,OAAO,SAAS,CAAC;gBAC/B,QAAQ;gBACR,SAAS;gBACT,YAAY;YACd;YACA,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG;YAChB,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,QAAQ;YACV,OAAO,KAAK;YACZ,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,MAAM,eAAe,OAAO,eAAe;YAC3C,IAAI,cAAc;gBAChB,mEAAmE;gBACnE,MAAM,UAAU,aAAa,QAAQ;gBACrC,MAAM,SAAS,IAAK,aAAa,WAAW,CAAS;gBACrD,OAAO,GAAG,CAAC;oBACT,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI;oBACjC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;gBACjC;gBACA,OAAO,GAAG,CAAC;gBACX,OAAO,eAAe,CAAC;gBACvB,OAAO,SAAS;YAClB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,gBAAgB,OAAO,gBAAgB;YAC7C,IAAI,cAAc,MAAM,EAAE;gBACxB,cAAc,OAAO,CAAC,CAAC,MAAQ,OAAO,MAAM,CAAC;gBAC7C,OAAO,mBAAmB;gBAC1B,OAAO,SAAS;YAClB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,AAAC,EAAE,MAAM,CAAiB,SAAS,CAAC,QAAQ,CAAC,gBAAgB;YAC/F,cAAc;YACd,cAAc;gBACZ,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;gBAChC,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC;YAClC;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,mBAAmB;gBACjB,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;gBAC3B,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;YAC7B;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,gDAAgD;IAChD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,WAAW;YAC1C;QACF;IACF,GAAG;QAAC;QAAY;KAAW;IAE3B,qBACE,8OAAC,4HAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBACL,MAAM,gBAAgB,CAAC;gBACvB,KAAK,gBAAgB,CAAC;gBACtB,UAAU;YACZ;YACA,aAAa;;8BAGb,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;sCAGhB,8OAAC,4HAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;8BAIP,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAAoD,aAAa,CAAC,IAAM,EAAE,eAAe;8BACrG,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,eAAe,KAAK,EAAE;wCAC/B,iBAAiB,IAAM,iBAAiB,KAAK,EAAE;wCAC/C,WAAU;wCACV,cAAY,KAAK,KAAK;kDAErB,KAAK,IAAI;;;;;;;;;;;8CAGd,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAG,KAAK,KAAK;;;;;;;;;;;;2BAZJ,KAAK,EAAE;;;;;;;;;;8BAkB3B,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAA0B,aAAa,CAAC,IAAM,EAAE,eAAe;;sCAC5E,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAU;;;;;;;;;;;;;;;;;;;;;sDAK5C,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,aAAa;4DAAa,iBAAiB;wDAAc;;;;;;;;;;;;;;;;;;;;;sDAK1E,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;gBAG3C,CAAC,eAAe,WAAW,eAAe,QAAQ,mBACjD;;sCACE,8OAAC;4BAAI,WAAU;4BAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;8CAC1F,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;wCAAC;qCAAU;oCAClB,eAAe,CAAC,QAAU,aAAa,KAAK,CAAC,EAAE;oCAC/C,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;wBAG9C,eAAe,yBACd,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAW;;;;;;;;;;;;;;;;;;;;;sDAK7C,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAGP,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;gBASvB,CAAC,eAAe,eAAe,eAAe,QAAQ,mBACrD,8OAAC;oBAAI,WAAU;oBAAwC,aAAa,CAAC,IAAM,EAAE,eAAe;;sCAC1F,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC,2HAAA,CAAA,SAAM;4BACL,OAAO;gCAAC;6BAAY;4BACpB,eAAe,CAAC,QAAU,eAAe,KAAK,CAAC,EAAE;4BACjD,KAAK;4BACL,KAAK;4BACL,MAAM;4BACN,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAA6B;;;;;;;;;;;;8BAIjD,8OAAC,8HAAA,CAAA,YAAS;oBAAC,aAAY;oBAAW,WAAU;;;;;;8BAG5C,8OAAC;oBAAI,WAAU;oBAA0B,aAAa,CAAC,IAAM,EAAE,eAAe;;sCAC5E,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;sCAGZ,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,OAAO,EAAE;wCACrC,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGpB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGhB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGlB,8OAAC,4HAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/design/ImageEditor/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ImageEditorProvider } from './context';\r\nimport Canvas from './Canvas';\r\nimport Toolbar from './Toolbar';\r\nimport { ReactNode } from 'react';\r\n\r\ninterface ImageEditorProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ImageEditor = ({ children }: ImageEditorProps) => {\r\n  return <ImageEditorProvider>{children}</ImageEditorProvider>;\r\n};\r\n\r\nImageEditor.Canvas = Canvas;\r\nImageEditor.Toolbar = Toolbar;\r\n\r\nexport default ImageEditor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAoB;IACjD,qBAAO,8OAAC,+IAAA,CAAA,sBAAmB;kBAAE;;;;;;AAC/B;AAEA,YAAY,MAAM,GAAG,8IAAA,CAAA,UAAM;AAC3B,YAAY,OAAO,GAAG,+IAAA,CAAA,UAAO;uCAEd", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/design/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport ImageEditor from '../../../../components/design/ImageEditor';\r\n\r\nexport default function DesignPage() {\r\n  return (\r\n    <ImageEditor>\r\n      <ImageEditor.Canvas />\r\n      <ImageEditor.Toolbar />\r\n    </ImageEditor>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC,6IAAA,CAAA,UAAW;;0BACV,8OAAC,6IAAA,CAAA,UAAW,CAAC,MAAM;;;;;0BACnB,8OAAC,6IAAA,CAAA,UAAW,CAAC,OAAO;;;;;;;;;;;AAG1B", "debugId": null}}]}