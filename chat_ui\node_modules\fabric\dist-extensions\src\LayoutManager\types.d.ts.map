{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/LayoutManager/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,mBAAmB,EACnB,aAAa,EACb,mBAAmB,EACpB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,KAAK,EACV,0BAA0B,EAC1B,iBAAiB,EACjB,sBAAsB,EACtB,mBAAmB,EACnB,2BAA2B,EAC3B,4BAA4B,EAC7B,MAAM,aAAa,CAAC;AAErB,MAAM,MAAM,aAAa,GACrB,OAAO,0BAA0B,GACjC,OAAO,4BAA4B,GACnC,OAAO,2BAA2B,GAClC,OAAO,iBAAiB,GACxB,OAAO,mBAAmB,GAC1B,OAAO,sBAAsB,CAAC;AAElC,MAAM,MAAM,oBAAoB,GAAG;IACjC;;OAEG;IACH,MAAM,EAAE,KAAK,CAAC;IAEd;;;;;;OAMG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC;IAEnB;;OAEG;IACH,kBAAkB,CAAC,EAAE,KAAK,CAAC;IAE3B;;OAEG;IACH,IAAI,EAAE,KAAK,CAAC;CACb,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,MAAM,CAAC,EAAE,oBAAoB,CAAC;IAC9B,UAAU,EAAE,KAAK,CAAC;IAClB,UAAU,EAAE,KAAK,CAAC;IAClB;;OAEG;IACH,MAAM,EAAE,KAAK,CAAC;CACf,CAAC;AAEF,KAAK,6BAA6B,GAAG;IACnC,SAAS,CAAC,EAAE,oBAAoB,CAAC;IACjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,6BAA6B,GAAG;IACpE,QAAQ,CAAC,EAAE,cAAc,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,MAAM,EAAE,KAAK,CAAC;IACd,QAAQ,CAAC,EAAE,cAAc,CAAC;IAC1B,IAAI,EAAE,aAAa,CAAC;IACpB;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,2BAA2B,GAAG,mBAAmB,GAAG;IAC9D,IAAI,EAAE,OAAO,0BAA0B,CAAC;IACxC,OAAO,EAAE,YAAY,EAAE,CAAC;IACxB,CAAC,CAAC,EAAE,MAAM,CAAC;IACX,CAAC,CAAC,EAAE,MAAM,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,6BAA6B,GAAG,mBAAmB,GAAG;IAChE,IAAI,EAAE,OAAO,iBAAiB,GAAG,OAAO,mBAAmB,CAAC;IAC5D,OAAO,EAAE,YAAY,EAAE,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,2BAA2B,GAAG,mBAAmB,GAAG;IAC9D,IAAI,EAAE,OAAO,2BAA2B,CAAC;IACzC,OAAO,EAAE,UAAU,CAAC;IACpB,CAAC,EAAE,aAAa,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,4BAA4B,GAAG,mBAAmB,GAAG;IAC/D,IAAI,EAAE,OAAO,4BAA4B,CAAC;CAC3C,GAAG,CACE;IACE,OAAO,EAAE,mBAAmB,CAAC;IAC7B,CAAC,EAAE,mBAAmB,CAAC;CACxB,GACD;IACE,OAAO,EAAE,SAAS,CAAC;IACnB,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;CAC3B,CACJ,CAAC;AAEJ,MAAM,MAAM,uBAAuB,GAAG,mBAAmB,GACvD,6BAA6B,GAAG;IAC9B,IAAI,EAAE,OAAO,sBAAsB,CAAC;CACrC,CAAC;AAEJ,MAAM,MAAM,aAAa,GACrB,2BAA2B,GAC3B,6BAA6B,GAC7B,2BAA2B,GAC3B,4BAA4B,GAC5B,uBAAuB,CAAC;AAE5B,MAAM,MAAM,mBAAmB,GAAG,aAAa,GAAG;IAChD,QAAQ,EAAE,cAAc,CAAC;IACzB,YAAY,CAAC,EAAE,cAAc,CAAC;IAC9B,OAAO,EAAE,OAAO,CAAC;IACjB,eAAe,IAAI,IAAI,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,OAAO,EAAE,YAAY,EAAE,CAAC;IACxB,MAAM,EAAE,KAAK,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,OAAO,EAAE,mBAAmB,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,OAAO,EAAE,mBAAmB,CAAC;IAC7B;;OAEG;IACH,MAAM,CAAC,EAAE,YAAY,CAAC;CACvB,CAAC"}