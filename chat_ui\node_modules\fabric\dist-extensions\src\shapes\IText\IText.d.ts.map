{"version": 3, "file": "IText.d.ts", "sourceRoot": "", "sources": ["../../../../src/shapes/IText/IText.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAO1D,OAAO,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1E,OAAO,KAAK,EAAE,mBAAmB,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAQnE,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,kBAAkB,CAAC;AACrE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAI3D,MAAM,MAAM,gBAAgB,GAAG;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AASF,eAAO,MAAM,kBAAkB,EAAE,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAkB/D,CAAC;AAGF,UAAU,gBAAgB;IACxB,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,oBACf,SAAQ,mBAAmB,EACzB,gBAAgB;CAAG;AAEvB,MAAM,WAAW,UAAW,SAAQ,SAAS,EAAE,gBAAgB;CAAG;AAElE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,qBAAa,KAAK,CACd,KAAK,SAAS,QAAQ,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,EACxD,MAAM,SAAS,oBAAoB,GAAG,oBAAoB,EAC1D,SAAS,SAAS,WAAW,GAAG,WAAW,CAE7C,SAAQ,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CACnD,YAAW,gBAAgB;IAE3B;;;;OAIG;IACK,cAAc,EAAE,MAAM,CAAC;IAE/B;;;;OAIG;IACK,YAAY,EAAE,MAAM,CAAC;IAErB,gBAAgB,EAAE,MAAM,CAAC;IAEzB,cAAc,EAAE,MAAM,CAAC;IAE/B;;;;OAIG;IACK,cAAc,EAAE,MAAM,CAAC;IAE/B;;;;OAIG;IACK,SAAS,EAAE,OAAO,CAAC;IAE3B;;;;OAIG;IACK,QAAQ,EAAE,OAAO,CAAC;IAE1B;;;;OAIG;IACK,kBAAkB,EAAE,MAAM,CAAC;IAEnC;;;;OAIG;IACK,WAAW,EAAE,MAAM,CAAC;IAE5B;;;;;;;OAOG;IACK,WAAW,EAAE,MAAM,CAAC;IAE5B;;;;OAIG;IACK,WAAW,EAAE,MAAM,CAAC;IAE5B;;;;OAIG;IACK,cAAc,EAAE,MAAM,CAAC;IAEvB,gBAAgB,EAAE,MAAM,CAAC;IAEjC;;;;OAIG;IACK,OAAO,EAAE,OAAO,CAAC;IAEzB,MAAM,CAAC,WAAW,2FAAsB;IAExC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAIzC,MAAM,CAAC,IAAI,SAAW;IAEtB,IAAI,IAAI,WAIP;IAED;;;;OAIG;gBACS,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK;IAKzC;;;;;OAKG;IACH,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAc5B;;;OAGG;IACH,iBAAiB,CAAC,KAAK,EAAE,MAAM;IAK/B;;;OAGG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM;IAK7B;;;;OAIG;IACH,SAAS,CAAC,cAAc,CACtB,QAAQ,EAAE,gBAAgB,GAAG,cAAc,EAC3C,KAAK,EAAE,MAAM;IASf;;;OAGG;IACH,qBAAqB;IAKrB;;;;;;OAMG;IACH,cAAc;IAKd;;;;;;;OAOG;IACH,kBAAkB,CAChB,UAAU,GAAE,MAAiC,EAC7C,QAAQ,GAAE,MAA0B,EACpC,QAAQ,CAAC,EAAE,OAAO;IAKpB;;;;;OAKG;IACH,kBAAkB,CAChB,MAAM,EAAE,MAAM,EACd,UAAU,GAAE,MAAiC,EAC7C,QAAQ,GAAE,MAA0B;IAKtC;;;;OAIG;IACH,mBAAmB,CACjB,cAAc,SAAsB,EACpC,YAAY,CAAC,EAAE,OAAO;;;;IAKxB;;;OAGG;IACH,MAAM,CAAC,GAAG,EAAE,wBAAwB;IAQpC;;;OAGG;IACH,eAAe,CAAC,OAAO,CAAC,EAAE,4BAA4B,GAAG,iBAAiB;IAQ1E;;;OAGG;IACH,uBAAuB;IA2DvB;;;;;;OAMG;IACH,yBAAyB,IAAI,YAAY,EAAE;IAc3C;;;;;;;OAOG;IACH,oBAAoB,CAClB,KAAK,GAAE,MAA4B,EACnC,WAAW,CAAC,EAAE,OAAO,GACpB,gBAAgB;IAYnB;;;;;OAKG;IACH,2BAA2B,CACzB,KAAK,EAAE,MAAM,EACb,WAAW,CAAC,EAAE,OAAO,GACpB;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE;IAUhC;;;;OAIG;IACH,4BAA4B,CAAC,KAAK,EAAE,MAAM;;;;IAwC1C;;;;OAIG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM;IAQrC;;;;OAIG;IACH,YAAY,CAAC,GAAG,EAAE,wBAAwB,EAAE,UAAU,EAAE,gBAAgB;IAIxE;;;;;OAKG;IACH,sBAAsB,CACpB,cAAc,GAAE,MAA4B,EAC5C,UAAU,GAAE,gBAA4D,GACvE,mBAAmB;IA2BtB;;;OAGG;IACH,aAAa,CACX,GAAG,EAAE,wBAAwB,EAC7B,UAAU,EAAE,gBAAgB,EAC5B,cAAc,EAAE,MAAM;IASxB;;;;OAIG;IACH,eAAe,CAAC,GAAG,EAAE,wBAAwB,EAAE,UAAU,EAAE,gBAAgB;IAY3E;;OAEG;IACH,sBAAsB;IAUtB,sBAAsB,CAAC,CAAC,EAAE,SAAS;IAKnC;;;;;;OAMG;IACH,gBAAgB,CACd,GAAG,EAAE,wBAAwB,EAC7B,SAAS,EAAE;QAAE,cAAc,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE,EAC3D,UAAU,EAAE,gBAAgB;IA+E9B;;;;;;OAMG;IACH,sBAAsB,IAAI,MAAM;IAKhC;;;;;;;OAOG;IACH,mBAAmB,IAAI,MAAM,GAAG,OAAO,GAAG,IAAI;IAK9C;;;OAGG;IACH,oBAAoB;;;;IAOpB,OAAO;CAKR"}