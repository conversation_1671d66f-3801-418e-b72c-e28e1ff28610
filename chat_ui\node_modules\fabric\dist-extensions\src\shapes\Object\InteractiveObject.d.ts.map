{"version": 3, "file": "InteractiveObject.d.ts", "sourceRoot": "", "sources": ["../../../../src/shapes/Object/InteractiveObject.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAQ,MAAM,aAAa,CAAC;AAC1C,OAAO,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAExC,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAQ9D,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AACrF,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AACnE,OAAO,KAAK,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,MAAM,SAAS,CAAC;AAKzE,MAAM,MAAM,OAAO,GAAG,KAAK,GAAG;IAC5B,MAAM,EAAE,YAAY,CAAC;IACrB,WAAW,EAAE,YAAY,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAElD,MAAM,MAAM,6BAA6B,GAAG,OAAO,CACjD,IAAI,CAAC,uBAAuB,EAAE,aAAa,GAAG,iBAAiB,CAAC,CACjE,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,6BAA6B,GACxD,6BAA6B,GAC7B,OAAO,CACL,IAAI,CAAC,uBAAuB,EAAE,YAAY,GAAG,aAAa,CAAC,GAAG;IAC5D,kBAAkB,EAAE,OAAO,CAAC;CAC7B,CACF,CAAC;AAEJ,qBAAa,uBAAuB,CAChC,KAAK,SAAS,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAC7D,MAAM,SAAS,qBAAqB,GAAG,qBAAqB,EAC5D,SAAS,SAAS,YAAY,GAAG,YAAY,CAE/C,SAAQ,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAC7C,YAAW,iBAAiB;IAEpB,YAAY,EAAE,OAAO,CAAC;IAEtB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB,aAAa,EAAE,OAAO,CAAC;IACvB,aAAa,EAAE,OAAO,CAAC;IACvB,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,EAAE,OAAO,CAAC;IACtB,eAAe,EAAE,OAAO,CAAC;IAEzB,UAAU,EAAE,MAAM,CAAC;IACnB,eAAe,EAAE,MAAM,CAAC;IACxB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,WAAW,EAAE,MAAM,GAAG,QAAQ,CAAC;IAC/B,eAAe,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACjC,WAAW,EAAE,OAAO,CAAC;IAErB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACjC,uBAAuB,EAAE,MAAM,CAAC;IAChC,iBAAiB,EAAE,MAAM,CAAC;IAC1B,UAAU,EAAE,OAAO,CAAC;IACpB,wBAAwB,EAAE,MAAM,CAAC;IAEjC,UAAU,EAAE,OAAO,CAAC;IACpB,OAAO,EAAE,OAAO,CAAC;IACjB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAExB,WAAW,EAAE,mBAAmB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAClD,UAAU,EAAE,mBAAmB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAEzD;;;;;OAKG;IACK,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAEzC;;;;;;;OAOG;IACK,QAAQ,CAAC,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACK,mBAAmB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAErD;;;OAGG;IACK,QAAQ,EAAE,WAAW,CAAC;IAE9B;;;OAGG;IACK,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;;;;OAOG;IACK,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAExB,MAAM,CAAC,WAAW,+IAAkC;IAEpD,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAOzC;;;OAGG;gBACS,OAAO,CAAC,EAAE,KAAK;IAU3B;;;;;OAKG;IACH,MAAM,CAAC,cAAc,IAAI;QAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;KAAE;IAI9D;;;;;OAKG;IACH,kBAAkB;IAiBlB,gBAAgB;;;;;IAWhB;;;;;;;;;;OAUG;IACH,WAAW,CACT,OAAO,EAAE,KAAK,EACd,QAAQ,UAAQ,GACf;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,OAAO,CAAA;KAAE,GAAG,SAAS;IA6BhE;;;;;;OAMG;IACH,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAuDtC;;;;;;OAMG;IACH,OAAO,CAAC,iBAAiB;IAqBzB;;;;OAIG;IACH,SAAS,IAAI,IAAI;IAKjB;;;;OAIG;IACH,cAAc,CACZ,EAAE,EAAE,CACF,OAAO,EAAE,OAAO,EAChB,GAAG,EAAE,MAAM,EACX,YAAY,EAAE,uBAAuB,KAClC,GAAG;IAOV;;;;;;;;;OASG;IACH,uBAAuB,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI;IAmB5D;;;;OAIG;IACH,aAAa,CAAC,GAAG,EAAE,wBAAwB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI;IAI/D;;;;;OAKG;IACH,YAAY,CACV,GAAG,EAAE,wBAAwB,EAC7B,IAAI,EAAE,KAAK,EACX,aAAa,GAAE,cAAmB,GACjC,IAAI;IAeP;;;;;;OAMG;IACH,eAAe,CACb,GAAG,EAAE,wBAAwB,EAC7B,aAAa,GAAE,cAAmB;IAiCpC;;;;;;;OAOG;IACH,WAAW,CACT,GAAG,EAAE,wBAAwB,EAC7B,OAAO,EAAE,eAAe,EACxB,aAAa,EAAE,cAAc,GAC5B,IAAI;IA4BP;;;;;;OAMG;IACH,2BAA2B,CACzB,GAAG,EAAE,wBAAwB,EAC7B,IAAI,EAAE,KAAK,GACV,IAAI;IAoBP;;;;;;;;;OASG;IACH,YAAY,CACV,GAAG,EAAE,wBAAwB,EAC7B,aAAa,GAAE,6BAAkC;IA0BnD;;;;;OAKG;IACH,gBAAgB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAO7C;;;;;;;OAOG;IACH,iBAAiB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;IAOtD;;;;OAIG;IACH,qBAAqB,CAAC,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAM;IAM3D;;;;;;;;;OASG;IACH,eAAe,CACb,eAAe,CAAC,EAAE,OAAO,GACxB,wBAAwB,GAAG,SAAS;IAqBvC;;;;;;;OAOG;IACH,UAAU,CAAC,QAAQ,CAAC,EAAE;QACpB,CAAC,CAAC,EAAE,aAAa,CAAC;QAClB,MAAM,CAAC,EAAE,uBAAuB,CAAC;KAClC,GAAG,OAAO;IAKX;;;;;OAKG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,aAAa,CAAA;KAAE,GAAG,OAAO;IAKnD;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,EAAE,aAAa;IAIrC;;;;OAIG;IACH,WAAW,CAAC,EAAE,EAAE,SAAS;IAIzB;;;;;OAKG;IACH,OAAO,CAAC,EAAE,EAAE,SAAS,GAAG,OAAO;IAI/B;;;;;;OAMG;IACH,sBAAsB,CAAC,EAAE,EAAE,SAAS;IAIpC;;;;;;;OAOG;IACH,sBAAsB,CAAC,EAAE,EAAE,SAAS;CAGrC"}