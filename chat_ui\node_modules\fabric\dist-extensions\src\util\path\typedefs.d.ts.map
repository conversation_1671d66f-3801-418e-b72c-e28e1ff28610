{"version": 3, "file": "typedefs.d.ts", "sourceRoot": "", "sources": ["../../../../src/util/path/typedefs.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAE7C,MAAM,MAAM,sBAAsB,CAAC,CAAC,SAAS,MAAM,IAAI;IACrD,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,OAAO,CAAC,EAAE,CAAC,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,MAAM,IAAI,sBAAsB,CAAC,CAAC,CAAC,GAAG;IACrE;;;OAGG;IACH,QAAQ,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;IACjC;;;OAGG;IACH,WAAW,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC;IACrC;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,sBAAsB,CAAC,GAAG,CAAC,GAAG;IACvD,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,uBAAuB,GAAG;IACpC,CAAC,EAAE,sBAAsB,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,sBAAsB,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC,EAAE,YAAY,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAC1B,uBAAuB,CAAC,MAAM,uBAAuB,CAAC,CAAC;AAEzD;;GAEG;AACH,MAAM,MAAM,cAAc,GACtB,CAAC,OAAO,EAAE,MAAM,CAAC,GACjB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,GAC/B,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,GAC7C,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,GAC3D,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,GACzE;IACE,OAAO,EAAE,MAAM;IACf,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,GACD;IACE,OAAO,EAAE,MAAM;IACf,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,GACD;IACE,OAAO,EAAE,MAAM;IACf,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AAEN;;GAEG;AACH,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACrD,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC7D,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACrE,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IACrC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IACrC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5D,KAAK,SAAS,CAAC,CAAC,SAAS,cAAc,IACrC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAEpE;;;GAGG;AACH,MAAM,MAAM,4BAA4B,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAChF,MAAM,MAAM,4BAA4B,GAAG;IACzC,OAAO,EAAE,GAAG;IACZ,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,MAAM;CACX,CAAC;AACF,MAAM,MAAM,oBAAoB,GAC5B,4BAA4B,GAC5B,4BAA4B,CAAC;AAEjC,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAE7D,MAAM,MAAM,0BAA0B,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9E,MAAM,MAAM,0BAA0B,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;AAChF,MAAM,MAAM,kBAAkB,GAC1B,0BAA0B,GAC1B,0BAA0B,CAAC;AAE/B,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,kBAAkB,CAAC,CAAC;AAEzD,MAAM,MAAM,oCAAoC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7E,MAAM,MAAM,oCAAoC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;AAC9E,MAAM,MAAM,4BAA4B,GACpC,oCAAoC,GACpC,oCAAoC,CAAC;AAEzC,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAAC,CAAC;AAE7E,MAAM,MAAM,kCAAkC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3E,MAAM,MAAM,kCAAkC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;AAC5E,MAAM,MAAM,0BAA0B,GAClC,kCAAkC,GAClC,kCAAkC,CAAC;AAEvC,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC,0BAA0B,CAAC,CAAC;AAEzE,MAAM,MAAM,+BAA+B,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC7D,MAAM,MAAM,+BAA+B,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC7D,MAAM,MAAM,uBAAuB,GAC/B,+BAA+B,GAC/B,+BAA+B,CAAC;AAEpC,MAAM,MAAM,iBAAiB,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC;AAEnE,MAAM,MAAM,gCAAgC,GAAG;IAC7C,OAAO,EAAE,GAAG;IACZ,cAAc,EAAE,MAAM;IACtB,cAAc,EAAE,MAAM;IACtB,cAAc,EAAE,MAAM;IACtB,cAAc,EAAE,MAAM;IACtB,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AACF,MAAM,MAAM,gCAAgC,GAAG;IAC7C,OAAO,EAAE,GAAG;IACZ,eAAe,EAAE,MAAM;IACvB,eAAe,EAAE,MAAM;IACvB,eAAe,EAAE,MAAM;IACvB,eAAe,EAAE,MAAM;IACvB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;CACd,CAAC;AACF,MAAM,MAAM,wBAAwB,GAChC,gCAAgC,GAChC,gCAAgC,CAAC;AAErC,MAAM,MAAM,kBAAkB,GAAG,SAAS,CAAC,wBAAwB,CAAC,CAAC;AAErE,MAAM,MAAM,wCAAwC,GAAG;IACrD,OAAO,EAAE,GAAG;IACZ,cAAc,EAAE,MAAM;IACtB,cAAc,EAAE,MAAM;IACtB,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AACF,MAAM,MAAM,wCAAwC,GAAG;IACrD,OAAO,EAAE,GAAG;IACZ,eAAe,EAAE,MAAM;IACvB,eAAe,EAAE,MAAM;IACvB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;CACd,CAAC;AACF,MAAM,MAAM,gCAAgC,GACxC,wCAAwC,GACxC,wCAAwC,CAAC;AAE7C,MAAM,MAAM,0BAA0B,GACpC,SAAS,CAAC,gCAAgC,CAAC,CAAC;AAE9C,MAAM,MAAM,oCAAoC,GAAG;IACjD,OAAO,EAAE,GAAG;IACZ,aAAa,EAAE,MAAM;IACrB,aAAa,EAAE,MAAM;IACrB,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AACF,MAAM,MAAM,oCAAoC,GAAG;IACjD,OAAO,EAAE,GAAG;IACZ,cAAc,EAAE,MAAM;IACtB,cAAc,EAAE,MAAM;IACtB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;CACd,CAAC;AACF,MAAM,MAAM,4BAA4B,GACpC,oCAAoC,GACpC,oCAAoC,CAAC;AAEzC,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAAC,CAAC;AAE7E,MAAM,MAAM,4CAA4C,GAAG;IACzD,OAAO,EAAE,GAAG;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AACF,MAAM,MAAM,4CAA4C,GAAG;IACzD,OAAO,EAAE,GAAG;IACZ,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;CACd,CAAC;AACF,MAAM,MAAM,oCAAoC,GAC5C,4CAA4C,GAC5C,4CAA4C,CAAC;AAEjD,MAAM,MAAM,8BAA8B,GACxC,SAAS,CAAC,oCAAoC,CAAC,CAAC;AAElD,MAAM,MAAM,yBAAyB,GAAG;IACtC,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,MAAM;IACf,OAAO,EAAE,MAAM;IACf,QAAQ,EAAE,OAAO;IACjB,QAAQ,EAAE,CAAC,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;CACb,CAAC;AACF,MAAM,MAAM,yBAAyB,GAAG;IACtC,OAAO,EAAE,GAAG;IACZ,OAAO,EAAE,MAAM;IACf,OAAO,EAAE,MAAM;IACf,QAAQ,EAAE,OAAO;IACjB,QAAQ,EAAE,CAAC,GAAG,CAAC;IACf,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;CACd,CAAC;AAEF,MAAM,MAAM,iBAAiB,GACzB,yBAAyB,GACzB,yBAAyB,CAAC;AAE9B,MAAM,MAAM,qBAAqB,CAAC,CAAC,SAAS,iBAAiB,IAC3D,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACnE,MAAM,MAAM,WAAW,GACnB,SAAS,CAAC,iBAAiB,CAAC,GAC5B,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;AAE7C;;GAEG;AAEH;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAC7B,oBAAoB,GACpB,kBAAkB,GAClB,4BAA4B,GAC5B,0BAA0B,GAC1B,uBAAuB,GACvB,wBAAwB,GACxB,gCAAgC,GAChC,4BAA4B,GAC5B,oCAAoC,GACpC,iBAAiB,CAAC;AAEtB;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,qBAAqB,EAAE,CAAC;AAEvD;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAC5B,4BAA4B,GAC5B,0BAA0B,GAC1B,+BAA+B,GAC/B,gCAAgC,GAChC,oCAAoC,CAAC;AAEzC,MAAM,MAAM,uBAAuB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAElE,MAAM,MAAM,yBAAyB,GACjC,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,GACH,GAAG,CAAC;AAER;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,oBAAoB,EAAE,CAAC;AAErD;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG,EAAE,GAAG;IAAE,KAAK,EAAE,OAAO,CAAA;CAAE,CAAC"}