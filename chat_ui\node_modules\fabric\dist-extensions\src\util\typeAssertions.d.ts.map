{"version": 3, "file": "typeAssertions.d.ts", "sourceRoot": "", "sources": ["../../../src/util/typeAssertions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;AAEjE,eAAO,MAAM,QAAQ,WACX,OAAO,GAAG,MAAM,GAAG,IAAI,KAC9B,MAAM,IAAI,OAEZ,CAAC;AAEF,eAAO,MAAM,oBAAoB,WACvB,OAAO,GAAG,MAAM,GAAG,IAAI,KAC9B,MAAM,IAAI,OAEZ,CAAC;AAEF,eAAO,MAAM,SAAS,WAAY,OAAO,KAAG,MAAM,IAAI,OAIrD,CAAC;AAEF,eAAO,MAAM,YAAY,kBACR,YAAY,KAC1B,YAAY,IAAI,UAKlB,CAAC;AAEF,eAAO,MAAM,MAAM,kBAAmB,YAAY,KAAG,YAAY,IAAI,IAOpE,CAAC;AAEF,eAAO,MAAM,iBAAiB,kBACb,YAAY,KAC1B,YAAY,IAAI,eACyC,CAAC"}