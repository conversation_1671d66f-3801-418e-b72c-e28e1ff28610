{"version": 3, "file": "WebGLFilterBackend.d.ts", "sourceRoot": "", "sources": ["../../../src/filters/WebGLFilterBackend.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EACV,mBAAmB,EACnB,aAAa,EACb,aAAa,EACb,kBAAkB,EACnB,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE/C,qBAAa,kBAAkB;IACrB,QAAQ,EAAE,MAAM,CAAC;IAEzB;;QAEI;IACJ,SAAS,EAAE,YAAY,CAA8C;IAErE;;;QAGI;IACI,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B,MAAM,EAAE,iBAAiB,CAAC;IAElC;;QAEI;IACI,EAAE,EAAE,qBAAqB,CAAC;IAElC;;QAEI;IACI,YAAY,EAAE,aAAa,CAAC;IAEpC;;QAEI;IACI,YAAY,EAAE,aAAa,CAAC;IAEpC;;QAEI;IACI,OAAO,EAAE,GAAG,CAAC;IAErB;;;;;;QAMI;IACJ,SAAS,EAAE,kBAAkB,CAAM;gBAEvB,EAAE,QAA6B,EAAE;;KAAK;IAMlD;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAKnD;;;OAGG;IACH,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAoBtD;;;;;;;;;;;OAWG;IACH,YAAY,CACV,OAAO,EAAE,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,EAClD,MAAM,EAAE,cAAc,EACtB,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,MAAM,EACd,YAAY,EAAE,iBAAiB,EAC/B,QAAQ,CAAC,EAAE,MAAM,GAChB,mBAAmB,GAAG,SAAS;IA8DlC;;OAEG;IACH,OAAO;IAYP;;OAEG;IACH,gBAAgB;IAKhB;;;;;;;;;;;;OAYG;IACH,aAAa,CACX,EAAE,EAAE,qBAAqB,EACzB,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,MAAM,EACd,kBAAkB,CAAC,EAAE,cAAc,EACnC,MAAM,CAAC,EACH,yBAAyB,CAAC,SAAS,CAAC,GACpC,yBAAyB,CAAC,QAAQ,CAAC;IA4CzC;;;;;;;;OAQG;IACH,gBAAgB,CACd,QAAQ,EAAE,MAAM,EAChB,kBAAkB,EAAE,cAAc,EAClC,MAAM,CAAC,EACH,yBAAyB,CAAC,SAAS,CAAC,GACpC,yBAAyB,CAAC,QAAQ,CAAC,GACtC,YAAY,GAAG,IAAI;IAmBtB;;;;;OAKG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM;IAOlC;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,EAAE,qBAAqB,EAAE,aAAa,EAAE,mBAAmB;IAwBxE;;;;;;;OAOG;IACH,sBAAsB,CACpB,IAAI,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAClC,EAAE,EAAE,qBAAqB,EACzB,aAAa,EAAE,mBAAmB;IAkBpC;;;;;;OAMG;IACH,cAAc;CAuBf"}