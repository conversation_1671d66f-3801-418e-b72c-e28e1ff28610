{"version": 3, "file": "Text.d.ts", "sourceRoot": "", "sources": ["../../../../src/shapes/Text/Text.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,KAAK,EACV,4BAA4B,EAC5B,SAAS,EACT,oBAAoB,EACrB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAG1C,OAAO,KAAK,EACV,SAAS,EACT,sBAAsB,EACtB,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACT,MAAM,gBAAgB,CAAC;AAIxB,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAQjE,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAGpC,OAAO,KAAK,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,MAAM,iBAAiB,CAAC;AAChF,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,aAAa,CAAC;AAevD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAmBtD,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC;AAEzC,MAAM,MAAM,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC;AAE1E,MAAM,MAAM,aAAa,GAAG;IAC1B,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC;IAC1B,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;CAC7B,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG;IACzB,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,CAAC;AAGF,UAAU,eAAe;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,GAAG,MAAM,CAAC;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,SAAS,CAAC;IACpB,SAAS,EAAE,UAAU,CAAC;IACtB,SAAS,EAAE,OAAO,CAAC;IACnB,QAAQ,EAAE,OAAO,CAAC;IAClB,WAAW,EAAE,OAAO,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,eAAe,CAAC;IAC3B,IAAI,CAAC,EAAE,IAAI,CAAC;IACZ,uBAAuB,EAAE,MAAM,CAAC;CACjC;AAED,MAAM,WAAW,mBACf,SAAQ,qBAAqB,EAC3B,eAAe;IACjB,MAAM,EAAE,cAAc,GAAG,SAAS,CAAC;CACpC;AAED,MAAM,WAAW,SAAU,SAAQ,iBAAiB,EAAE,eAAe;IACnE,MAAM,EAAE,SAAS,CAAC;CACnB;AAED;;;GAGG;AACH,qBAAa,UAAU,CACnB,KAAK,SAAS,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,EACtD,MAAM,SAAS,mBAAmB,GAAG,mBAAmB,EACxD,SAAS,SAAS,YAAY,GAAG,YAAY,CAE/C,SAAQ,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAC3C,YAAW,eAAe;IAE1B;;;;OAIG;IACH,MAAM,CAAC,oBAAoB,EAAE,MAAM,EAAE,CAAwB;IAE7D;;OAEG;IACK,UAAU,EAAE,MAAM,CAAC;IAE3B;;;;OAIG;IACK,gBAAgB,EAAE,MAAM,CAAC;IAEjC;;;;OAIG;IACK,cAAc,EAAE,MAAM,CAAC;IAE/B;;;;OAIG;IACK,QAAQ,EAAE,MAAM,CAAC;IAEjB,IAAI,EAAE,MAAM,CAAC;IAErB;;;;OAIG;IACK,QAAQ,EAAE,MAAM,CAAC;IAEzB;;;;OAIG;IACK,UAAU,EAAE,MAAM,GAAG,MAAM,CAAC;IAEpC;;;;OAIG;IACK,UAAU,EAAE,MAAM,CAAC;IAE3B;;;;OAIG;IACK,SAAS,EAAE,OAAO,CAAC;IAE3B;;;;OAIG;IACK,QAAQ,EAAE,OAAO,CAAC;IAE1B;;;;OAIG;IACK,WAAW,EAAE,OAAO,CAAC;IAE7B;;;;;OAKG;IACK,SAAS,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACK,SAAS,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACK,UAAU,EAAE,MAAM,CAAC;IAE3B;;OAEG;IACK,WAAW,EAAE;QACnB;;;WAGG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;;WAGG;QACH,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IAEF;;OAEG;IACK,SAAS,EAAE;QACjB;;;WAGG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;;WAGG;QACH,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IAEF;;;;OAIG;IACK,mBAAmB,EAAE,MAAM,CAAC;IAE5B,MAAM,EAAE,SAAS,CAAC;IAE1B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACK,IAAI,CAAC,EAAE,IAAI,CAAC;IAEpB;;;;;;;;;;;OAWG;IACK,uBAAuB,EAAE,MAAM,CAAC;IAExC;;;;OAIG;IACK,eAAe,EAAE,MAAM,CAAC;IAEhC;;;;;OAKG;IACK,QAAQ,EAAE,SAAS,CAAC;IAE5B;;;;;;;OAOG;IACK,SAAS,EAAE,UAAU,CAAC;IAE9B;;OAEG;IACK,iBAAiB,EAAE,MAAM,CAAC;IAElC;;OAEG;IACK,OAAO,EAAE;QAAE,SAAS,EAAE,MAAM,CAAC;QAAC,WAAW,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IAE9E;;;;OAIG;IACK,aAAa,EAAE,MAAM,CAAC;IAE9B;;;;;OAKG;IACK,WAAW,EAAE,MAAM,CAAC;IAE5B;;;;OAIG;IACK,MAAM,EAAE,MAAM,CAAC;IAEvB;;;;;;;;;;OAUG;IACK,SAAS,EAAE,eAAe,CAAC;IAEnC;;;;;OAKG;IACH,YAAY,EAAE,YAAY,EAAE,EAAE,CAAM;IAEpC;;;;;;OAMG;IACK,eAAe,EAAE,MAAM,CAAC;IAEhC;;;;OAIG;IACK,cAAc,EAAE,MAAM,CAAC;IAE/B;;;;;OAKG;IACK,SAAS,EAAE,MAAM,EAAE,CAAC;IAE5B;;;;OAIG;IACK,UAAU,EAAE,MAAM,EAAE,EAAE,CAAC;IAEvB,mBAAmB,EAAE,MAAM,EAAE,EAAE,CAAC;IAChC,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,WAAW,CAAC,EAAE,IAAI,CAAC;IAE3B,MAAM,CAAC,eAAe,WAA4C;IAElE,MAAM,CAAC,WAAW,+FAAqB;IAEvC,MAAM,CAAC,IAAI,SAAU;IAErB,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;gBAI7B,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK;IAgBzC;;;OAGG;IACH,WAAW;IAOX;;;OAGG;IACH,UAAU,IAAI,aAAa;IAS3B;;;;OAIG;IACH,cAAc;IAkBd;;OAEG;IACH,aAAa;IAuCb;;;;OAIG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAI3C;;;;;OAKG;IACH,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC;IAKtE;;;;OAIG;IACH,mBAAmB,CAAC,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,OAAO;;;;IAsBlE;;;OAGG;IACH,QAAQ,IAAI,MAAM;IAMlB;;;;;;;;;;OAUG;IACH,yBAAyB,IAAI,sBAAsB;IAQnD;;;OAGG;IACH,OAAO,CAAC,GAAG,EAAE,wBAAwB;IAWrC;;;OAGG;IACH,WAAW,CAAC,GAAG,EAAE,wBAAwB;IAUzC;;;;;;;;;OASG;IACH,cAAc,CACZ,GAAG,EAAE,wBAAwB,EAC7B,SAAS,CAAC,EAAE,GAAG,EACf,YAAY,CAAC,EAAE,OAAO;IAmBxB;;;;;OAKG;IACH,aAAa,IAAI,MAAM;IAYvB;;;;;;;;OAQG;IACH,eAAe,CACb,MAAM,EAAE,UAAU,GAAG,YAAY,EACjC,GAAG,EAAE,wBAAwB,EAC7B,IAAI,EAAE,MAAM,EAAE,EACd,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,MAAM;IAKnB;;;;OAIG;IACH,0BAA0B,CAAC,GAAG,EAAE,wBAAwB;IAkFxD;;;;;;;;;OASG;IACH,YAAY,CACV,KAAK,EAAE,MAAM,EACb,SAAS,EAAE,4BAA4B,EACvC,YAAY,EAAE,MAAM,GAAG,SAAS,EAChC,aAAa,EAAE,4BAA4B,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC;;;;IAsDrE;;;;;OAKG;IACH,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM;IAIpD;;;OAGG;IACH,WAAW,CAAC,SAAS,EAAE,MAAM;;;;IAW7B;;;;;OAKG;IACH,YAAY,CAAC,SAAS,EAAE,MAAM;;;;IAiE9B;;;;;;;OAOG;IACH,kBAAkB,CAAC,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY;IAWrE;;;;;;;OAOG;IACH,eAAe,CACb,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,YAAY,CAAC,EAAE,MAAM,EACrB,QAAQ,CAAC,EAAE,OAAO,GACjB,YAAY;IA+Bf;;;;OAIG;IACH,eAAe,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAgB1C;;OAEG;IACH,cAAc;IAUd;;;OAGG;IACH,cAAc,IAAI,MAAM;IAIxB;;;OAGG;IACH,aAAa,IAAI,MAAM;IAIvB;;;;OAIG;IACH,iBAAiB,CACf,GAAG,EAAE,wBAAwB,EAC7B,MAAM,EAAE,UAAU,GAAG,YAAY;IAuBnC;;;OAGG;IACH,eAAe,CAAC,GAAG,EAAE,wBAAwB;IAQ7C;;;OAGG;IACH,iBAAiB,CAAC,GAAG,EAAE,wBAAwB;IAiB/C;;;;;;;;OAQG;IACH,YAAY,CACV,MAAM,EAAE,UAAU,GAAG,YAAY,EACjC,GAAG,EAAE,wBAAwB,EAC7B,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,EAChB,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,MAAM;IAgGnB;;;;;;;;;;OAUG;IACH,kCAAkC,CAAC,MAAM,EAAE,OAAO;IAwBlD,YAAY,CAAC,CAAC,SAAS,MAAM,GAAG,QAAQ,EACtC,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,GAAG,CAAC,OAAO,EACrB,MAAM,EAAE,OAAO,GAAG,MAAM,GACvB;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE;IA6BvC;;;;;;OAMG;IACH,gBAAgB,CACd,GAAG,EAAE,wBAAwB,EAC7B,EACE,MAAM,EACN,WAAW,GACZ,EAAE,IAAI,CAAC,4BAA4B,EAAE,QAAQ,GAAG,aAAa,CAAC;iBAzCnD,MAAM;iBAAW,MAAM;;IAmDrC;;;;;;OAMG;IACH,cAAc,CAAC,GAAG,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;iBA1D5D,MAAM;iBAAW,MAAM;;IA8DrC;;;;;;;;;;OAUG;IACH,WAAW,CACT,MAAM,EAAE,UAAU,GAAG,YAAY,EACjC,GAAG,EAAE,wBAAwB,EAC7B,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM;IA2Cb;;;;OAIG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAIzC;;;;OAIG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM;IAIvC;;;;;;OAMG;IACH,SAAS,CAAC,UAAU,CAClB,KAAK,EAAE,MAAM,EACb,GAAG,EAAE,MAAM,EACX,MAAM,EAAE;QACN,IAAI,EAAE,MAAM,CAAC;QACb,QAAQ,EAAE,MAAM,CAAC;KAClB;IAgBH;;;;OAIG;IACH,kBAAkB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IA2C7C;;OAEG;IACH,WAAW;IAOX;;;;;;OAMG;IACH,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;IAUvC,sBAAsB;IAOtB;;;;;;OAMG;IACH,oBAAoB,CAAC,CAAC,SAAS,mBAAmB,EAChD,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,CAAC,GACV,IAAI,CAAC,CAAC,CAAC;IAKV;;;OAGG;IACH,qBAAqB,CACnB,GAAG,EAAE,wBAAwB,EAC7B,IAAI,EAAE,WAAW,GAAG,aAAa,GAAG,UAAU;IAqHhD;;;;OAIG;IACH,mBAAmB,CACjB,EACE,UAA4B,EAC5B,SAA0B,EAC1B,UAA4B,EAC5B,QAAwB,GACzB,GAAE,OAAO,CACR,IAAI,CACF,oBAAoB,EACpB,YAAY,GAAG,WAAW,GAAG,YAAY,GAAG,UAAU,CACvD,CACG,EACN,YAAY,CAAC,EAAE,OAAO,GACrB,MAAM;IAgBT;;;OAGG;IACH,MAAM,CAAC,GAAG,EAAE,wBAAwB;IAkBpC;;;;;;;OAOG;IACH,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE;IAItC;;;;OAIG;IACH,mBAAmB,CAAC,IAAI,EAAE,MAAM,GAAG,aAAa;IAkBhD;;;;OAIG;IACH,QAAQ,CACN,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC,EAC5D,CAAC,SAAS,MAAM,CAAC,GAAG,KAAK,EACzB,mBAAmB,GAAE,CAAC,EAAO,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM;IAQrD,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG;IA2BlC;;;OAGG;IACH,UAAU,IAAI,MAAM;IAIpB;;;OAGG;IACH,MAAM,CAAC,YAAY,WAcjB;IAIF;;;;;OAKG;IACH,MAAM,CAAC,eAAe,WAYpB;IAEF;;;;;;OAMG;WACU,WAAW,CACtB,OAAO,EAAE,WAAW,EACpB,OAAO,EAAE,SAAS,EAClB,QAAQ,CAAC,EAAE,QAAQ;;;;;;;;;;IAqErB;;;;OAIG;IACH,MAAM,CAAC,UAAU,CACf,CAAC,SAAS,QAAQ,CAAC,mBAAmB,CAAC,EACvC,CAAC,SAAS,UAAU,EACpB,MAAM,EAAE,CAAC;CAWZ"}